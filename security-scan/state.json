{"session_id": "security-scan-20250814", "total_vulnerabilities": 4, "fixed": 3, "critical": 0, "high": 0, "medium": 0, "low": 1, "vulnerabilities": [{"id": "CRIT-001", "severity": "critical", "title": "Hardcoded Secrets in Test Files", "file": "intelligence-engine/test-phase5-implementation.js", "lines": "179-183", "status": "fixed", "description": "Test file contains API key patterns for Stripe, OpenAI, Slack, AWS", "fixed_date": "2025-08-14", "fix_details": "Replaced hardcoded API keys with environment variable patterns"}, {"id": "MED-001", "severity": "medium", "title": "Weak JWT Secret Placeholders", "file": "multiple", "status": "partially_fixed", "description": "Default JWT_SECRET=your-secret-key placeholders found", "fixed_date": "2025-08-14", "fix_details": "Fixed in intelligence-engine/document-customizer.js (line 715). Note: .ai-workflow/intelligence-engine/document-customizer.js requires manual fix due to file permissions"}, {"id": "LOW-001", "severity": "low", "title": "HTTP URLs in Documentation", "file": "multiple", "status": "pending", "description": "Non-encrypted HTTP URLs found in docs and config"}, {"id": "LOW-002", "severity": "low", "title": "Dependencies Need Audit", "file": "package.json files", "status": "fixed", "description": "Package dependencies require vulnerability scanning", "fixed_date": "2025-08-14", "fix_details": "npm audit completed on main package.json and engine/package.json - 0 vulnerabilities found"}], "scan_date": "2025-08-14", "last_update": "2025-08-14", "next_action": "Complete JWT_SECRET fix in .ai-workflow file and review HTTP URLs"}