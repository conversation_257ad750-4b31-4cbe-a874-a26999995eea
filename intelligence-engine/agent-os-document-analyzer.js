#!/usr/bin/env node

/**
 * Agent-OS Document Analyzer
 * Comprehensive analysis of Agent-OS documents with customization detection and intelligent update strategies
 * 
 * This component provides sophisticated document analysis capabilities for Agent-OS projects:
 * - Analyzes existing Agent-OS documents to understand current state
 * - Detects user customizations and modifications using advanced pattern matching
 * - Calculates intelligent update strategies for merging new templates with existing content
 * - Preserves user content during updates while applying improvements
 * - Provides detailed reporting on document changes and merge strategies
 * 
 * Features:
 * - Multi-format document analysis (Markdown, JSON, YAML, JS, Shell)
 * - Advanced customization detection using AST parsing and pattern matching
 * - Intelligent merge strategies with conflict resolution
 * - Version-aware document comparison
 * - Integration with CustomizationManager and SharedMemoryStore
 * - Comprehensive reporting and analytics
 * - Event-driven architecture for real-time updates
 * 
 * <AUTHOR> Code
 * @date August 2025
 * @version 1.0.0
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const EventEmitter = require('events');

class AgentOSDocumentAnalyzer extends EventEmitter {
  constructor(options = {}) {
    super();
    
    // Configuration
    this.projectRoot = options.projectRoot || process.cwd();
    this.sharedMemory = options.sharedMemory;
    this.customizationManager = options.customizationManager;
    
    // Core analysis components
    this.documentCache = new Map();
    this.analysisResults = new Map();
    this.updateStrategiesCache = new Map();
    this.mergeReports = new Map();
    this.templateRegistry = new Map();
    
    // Analysis statistics
    this.stats = {
      documentsAnalyzed: 0,
      customizationsDetected: 0,
      updateStrategiesGenerated: 0,
      mergeConflicts: 0,
      preservedUserContent: 0,
      successfulMerges: 0,
      analysisTime: 0
    };
    
    // Agent-OS document patterns and signatures
    this.agentOSPatterns = {
      // File patterns that indicate Agent-OS documents
      filePatterns: [
        /^CLAUDE\.md$/i,
        /^README\.md$/i,
        /^\.claude\/.*$/,
        /^agent-os\.json$/i,
        /^package\.json$/,
        /^docker.*$/i,
        /^compose.*\.ya?ml$/i,
        /^\.env\.example$/,
        /install.*\.sh$/i,
        /setup.*\.sh$/i
      ],
      
      // Content signatures that identify Agent-OS generated content
      contentSignatures: [
        /# Claude Configuration/i,
        /## Agent-OS/i,
        /Generated by Agent-OS/i,
        /claude-flow/i,
        /hive-mind/i,
        /sub-agent/i,
        /@agent-os/i,
        /AGENT_OS_VERSION/i,
        /# DO NOT MODIFY - Agent-OS Managed/i
      ],
      
      // Template markers
      templateMarkers: [
        /{{.*?}}/g,  // Handlebars-style
        /\$\{.*?\}/g,  // Template literals
        /__.*?__/g,   // Double underscore placeholders
        /\[\[.*?\]\]/g  // Double bracket placeholders
      ]
    };
    
    // Document type analyzers
    this.analyzers = {
      'markdown': this.analyzeMarkdownDocument.bind(this),
      'json': this.analyzeJSONDocument.bind(this),
      'yaml': this.analyzeYAMLDocument.bind(this),
      'javascript': this.analyzeJavaScriptDocument.bind(this),
      'shell': this.analyzeShellDocument.bind(this),
      'dockerfile': this.analyzeDockerDocument.bind(this),
      'text': this.analyzeTextDocument.bind(this)
    };
    
    // Update strategy calculators
    this.updateStrategies = {
      'preserve-user': this.calculatePreserveUserStrategy.bind(this),
      'merge-intelligent': this.calculateIntelligentMergeStrategy.bind(this),
      'overwrite-safe': this.calculateSafeOverwriteStrategy.bind(this),
      'version-control': this.calculateVersionControlStrategy.bind(this),
      'incremental': this.calculateIncrementalStrategy.bind(this)
    };
    
    this.isInitialized = false;
    this.init();
  }
  
  /**
   * Initialize the document analyzer
   */
  async init() {
    try {
      // Initialize template registry
      await this.initializeTemplateRegistry();
      
      // Load existing analysis cache if available
      await this.loadAnalysisCache();
      
      this.isInitialized = true;
      this.emit('initialized', { analyzerReady: true });
      
      console.log('AgentOSDocumentAnalyzer initialized successfully');
      
    } catch (error) {
      this.emit('error', new Error(`Failed to initialize AgentOSDocumentAnalyzer: ${error.message}`));
      throw error;
    }
  }
  
  /**
   * Analyze existing Agent-OS documents in a project
   * @param {string} projectPath - Path to the project to analyze
   * @param {object} options - Analysis options
   */
  async analyzeExistingDocs(projectPath, options = {}) {
    const startTime = Date.now();
    
    try {
      // Validate project path
      if (!projectPath) {
        throw new Error('Project path is required');
      }
      
      const normalizedPath = path.resolve(projectPath);
      
      // Check if path exists
      try {
        await fs.access(normalizedPath);
      } catch (error) {
        throw new Error(`Project path does not exist: ${normalizedPath}`);
      }
      
      const opts = {
        recursive: options.recursive !== false, // Default to true
        includeIgnored: options.includeIgnored || false,
        analysisDepth: options.analysisDepth || 'full', // 'basic', 'standard', 'full', 'deep'
        cacheResults: options.cacheResults !== false,
        detectCustomizations: options.detectCustomizations !== false,
        ...options
      };
      
      console.log(`Starting document analysis for project: ${normalizedPath}`);
      
      // Discover Agent-OS documents
      const documents = await this.discoverAgentOSDocuments(normalizedPath, opts);
      
      console.log(`Found ${documents.length} potential Agent-OS documents`);
      
      // Analyze each document
      const analysisResults = new Map();
      const analysisPromises = documents.map(async (docPath) => {
        try {
          const result = await this.analyzeDocument(docPath, opts);
          if (result) {
            analysisResults.set(docPath, result);
          }
        } catch (error) {
          console.warn(`Failed to analyze document ${docPath}:`, error.message);
          analysisResults.set(docPath, {
            error: error.message,
            analyzed: false,
            path: docPath
          });
        }
      });
      
      await Promise.all(analysisPromises);
      
      // Generate project-level analysis
      const projectAnalysis = await this.generateProjectAnalysis(
        normalizedPath, 
        analysisResults, 
        opts
      );
      
      // Cache results if enabled
      if (opts.cacheResults && this.sharedMemory) {
        await this.cacheAnalysisResults(normalizedPath, projectAnalysis);
      }
      
      // Update statistics
      this.stats.documentsAnalyzed += documents.length;
      this.stats.analysisTime += Date.now() - startTime;
      
      this.emit('analysis-complete', {
        projectPath: normalizedPath,
        documentsCount: documents.length,
        analysisResults: projectAnalysis,
        duration: Date.now() - startTime
      });
      
      return projectAnalysis;
      
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }
  
  /**
   * Detect user customizations in a document
   * @param {object|string} docOrOriginal - Document analysis result or original content
   * @param {object|string} optionsOrCustomized - Detection options or customized content
   */
  async detectCustomizations(docOrOriginal, optionsOrCustomized = {}) {
    try {
      // Handle dual signature: (doc, options) or (originalContent, customizedContent)
      let doc, options;
      
      if (typeof docOrOriginal === 'string' && typeof optionsOrCustomized === 'string') {
        // Called with (originalContent, customizedContent)
        const originalContent = docOrOriginal;
        const customizedContent = optionsOrCustomized;
        
        // Use customization manager directly if available
        if (this.customizationManager && this.customizationManager.detectCustomizations) {
          return await this.customizationManager.detectCustomizations(
            originalContent,
            customizedContent,
            { filePath: 'comparison' }
          );
        }
        
        // Fallback to simple diff-based detection
        const customizations = [];
        const originalLines = originalContent.split('\n');
        const customizedLines = customizedContent.split('\n');
        
        // Find added lines
        customizedLines.forEach((line, index) => {
          if (!originalLines.includes(line) && line.trim()) {
            customizations.push({
              type: 'addition',
              line: index + 1,
              content: line,
              category: line.includes('USER:') ? 'user-comment' : 'user-content'
            });
          }
        });
        
        return customizations;
      }
      
      // Original behavior: (doc, options)
      doc = docOrOriginal;
      options = optionsOrCustomized;
      
      if (!doc || !doc.content) {
        throw new Error('Document content is required for customization detection');
      }
      
      const opts = {
        useCustomizationManager: options.useCustomizationManager !== false,
        analyzePatterns: options.analyzePatterns !== false,
        detectStructuralChanges: options.detectStructuralChanges !== false,
        analyzeSemanticChanges: options.analyzeSemanticChanges !== false,
        includeMetadata: options.includeMetadata !== false,
        ...options
      };
      
      let customizations = [];
      
      // Use CustomizationManager if available
      if (this.customizationManager && opts.useCustomizationManager) {
        const templateContent = await this.getOriginalTemplate(doc);
        if (templateContent) {
          const detectedCustomizations = await this.customizationManager.detectCustomizations(
            templateContent,
            doc.content,
            { filePath: doc.path }
          );
          customizations.push(...detectedCustomizations);
        }
      }
      
      // Analyze Agent-OS specific patterns
      if (opts.analyzePatterns) {
        const patternCustomizations = await this.detectAgentOSPatternCustomizations(doc);
        customizations.push(...patternCustomizations);
      }
      
      // Detect structural changes
      if (opts.detectStructuralChanges) {
        const structuralCustomizations = await this.detectStructuralCustomizations(doc);
        customizations.push(...structuralCustomizations);
      }
      
      // Analyze semantic changes
      if (opts.analyzeSemanticChanges) {
        const semanticCustomizations = await this.detectSemanticCustomizations(doc);
        customizations.push(...semanticCustomizations);
      }
      
      // Deduplicate and rank customizations
      const uniqueCustomizations = this.deduplicateCustomizations(customizations);
      const rankedCustomizations = this.rankCustomizations(uniqueCustomizations);
      
      // Store results
      doc.customizations = rankedCustomizations;
      
      // Update statistics
      this.stats.customizationsDetected += rankedCustomizations.length;
      
      this.emit('customizations-detected', {
        document: doc.path,
        customizations: rankedCustomizations,
        count: rankedCustomizations.length
      });
      
      return rankedCustomizations;
      
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }
  
  /**
   * Calculate update strategy for merging existing document with new template
   * @param {object} existing - Existing document analysis
   * @param {object} newTemplate - New template content/analysis
   * @param {object} options - Strategy options
   */
  async calculateUpdateStrategy(existing, newTemplate, options = {}) {
    try {
      if (!existing || !newTemplate) {
        throw new Error('Both existing document and new template are required');
      }
      
      const opts = {
        strategy: options.strategy || 'merge-intelligent',
        preserveUserContent: options.preserveUserContent !== false,
        allowDestructiveUpdates: options.allowDestructiveUpdates || false,
        versionControl: options.versionControl !== false,
        generateReport: options.generateReport !== false,
        riskLevel: options.riskLevel || 'medium', // 'low', 'medium', 'high'
        ...options
      };
      
      // Analyze differences between existing and new
      const differences = await this.compareDocuments(existing, newTemplate);
      
      // Detect merge conflicts
      const conflicts = await this.detectMergeConflicts(existing, newTemplate, differences);
      
      // Calculate strategy based on analysis
      const strategyCalculator = this.updateStrategies[opts.strategy] || 
                                 this.updateStrategies['merge-intelligent'];
      
      const strategy = await strategyCalculator(existing, newTemplate, {
        differences,
        conflicts,
        ...opts
      });
      
      // Generate merge preview if requested
      if (opts.generatePreview) {
        strategy.preview = await this.generateMergePreview(existing, newTemplate, strategy);
      }
      
      // Store strategy for future reference
      const strategyKey = this.generateStrategyKey(existing.path, newTemplate);
      this.updateStrategiesCache.set(strategyKey, strategy);
      
      // Update statistics
      this.stats.updateStrategiesGenerated++;
      if (conflicts.length > 0) {
        this.stats.mergeConflicts += conflicts.length;
      }
      
      this.emit('strategy-calculated', {
        document: existing.path,
        strategy: opts.strategy,
        conflicts: conflicts.length,
        riskLevel: strategy.riskLevel
      });
      
      return strategy;
      
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }
  
  /**
   * Preserve user content during document updates
   * @param {object} existing - Existing document with customizations
   * @param {object} template - New template to merge
   * @param {object} options - Preservation options
   */
  async preserveUserContent(existing, template, options = {}) {
    try {
      if (!existing || !template) {
        throw new Error('Both existing document and template are required');
      }
      
      const opts = {
        preserveComments: options.preserveComments !== false,
        preserveCustomSections: options.preserveCustomSections !== false,
        preserveConfigModifications: options.preserveConfigModifications !== false,
        preservePersonalAdditions: options.preservePersonalAdditions !== false,
        preserveOverrides: options.preserveOverrides !== false,
        generateBackup: options.generateBackup !== false,
        ...options
      };
      
      // Extract user customizations if not already done
      if (!existing.customizations) {
        await this.detectCustomizations(existing);
      }
      
      // Create preservation strategy
      const preservationStrategy = {
        customizations: [],
        insertionPoints: [],
        conflicts: [],
        preservedContent: {},
        strategy: 'preserve-and-merge'
      };
      
      // Process each type of customization
      for (const customization of existing.customizations || []) {
        const preserved = await this.preserveCustomization(
          customization, 
          template, 
          opts
        );
        
        if (preserved) {
          preservationStrategy.customizations.push(preserved);
          preservationStrategy.preservedContent[customization.type] = 
            (preservationStrategy.preservedContent[customization.type] || 0) + 1;
        }
      }
      
      // Find optimal insertion points in new template
      preservationStrategy.insertionPoints = await this.findInsertionPoints(
        template,
        preservationStrategy.customizations
      );
      
      // Apply preservation strategy to create merged content
      const mergedContent = await this.applyPreservationStrategy(
        template,
        preservationStrategy,
        opts
      );
      
      // Update statistics
      this.stats.preservedUserContent += preservationStrategy.customizations.length;
      
      this.emit('content-preserved', {
        document: existing.path,
        customizations: preservationStrategy.customizations.length,
        preservedTypes: Object.keys(preservationStrategy.preservedContent)
      });
      
      return {
        mergedContent,
        preservationStrategy,
        stats: preservationStrategy.preservedContent
      };
      
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }
  
  /**
   * Extract custom sections from document content
   * @param {string} content - Document content to analyze
   * @param {object} options - Extraction options
   */
  async extractCustomSections(content, options = {}) {
    try {
      if (!content || typeof content !== 'string') {
        throw new Error('Content must be a non-empty string');
      }
      
      const opts = {
        includeComments: options.includeComments !== false,
        includeMarkedSections: options.includeMarkedSections !== false,
        includeUserAdditions: options.includeUserAdditions !== false,
        detectImplicitSections: options.detectImplicitSections || false,
        ...options
      };
      
      const customSections = [];
      
      // Extract explicitly marked custom sections
      if (opts.includeMarkedSections) {
        const markedSections = this.extractMarkedCustomSections(content);
        customSections.push(...markedSections);
      }
      
      // Extract user comments
      if (opts.includeComments) {
        const userComments = this.extractUserComments(content);
        customSections.push(...userComments);
      }
      
      // Extract user additions
      if (opts.includeUserAdditions) {
        const userAdditions = this.extractUserAdditions(content);
        customSections.push(...userAdditions);
      }
      
      // Detect implicit custom sections
      if (opts.detectImplicitSections) {
        const implicitSections = await this.detectImplicitCustomSections(content);
        customSections.push(...implicitSections);
      }
      
      // Sort by position in document
      customSections.sort((a, b) => (a.position?.start || 0) - (b.position?.start || 0));
      
      return customSections;
      
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }
  
  /**
   * Compare two documents for differences
   * @param {object} doc1 - First document
   * @param {object} doc2 - Second document
   * @param {object} options - Comparison options
   */
  async compareDocuments(doc1, doc2, options = {}) {
    try {
      if (!doc1 || !doc2) {
        throw new Error('Both documents are required for comparison');
      }
      
      const opts = {
        compareContent: options.compareContent !== false,
        compareStructure: options.compareStructure !== false,
        compareMetadata: options.compareMetadata !== false,
        ignoreWhitespace: options.ignoreWhitespace || false,
        ignoreComments: options.ignoreComments || false,
        diffAlgorithm: options.diffAlgorithm || 'intelligent',
        ...options
      };
      
      const comparison = {
        contentChanges: [],
        structuralChanges: [],
        metadataChanges: [],
        similarity: 0,
        changesSummary: {},
        riskLevel: 'low'
      };
      
      // Compare content
      if (opts.compareContent) {
        comparison.contentChanges = await this.compareContent(
          doc1.content, 
          doc2.content, 
          opts
        );
      }
      
      // Compare structure
      if (opts.compareStructure) {
        comparison.structuralChanges = await this.compareStructure(
          doc1.structure || await this.analyzeDocumentStructure(doc1),
          doc2.structure || await this.analyzeDocumentStructure(doc2),
          opts
        );
      }
      
      // Compare metadata
      if (opts.compareMetadata) {
        comparison.metadataChanges = await this.compareMetadata(
          doc1.metadata || {},
          doc2.metadata || {},
          opts
        );
      }
      
      // Calculate similarity score
      comparison.similarity = this.calculateSimilarity(comparison);
      
      // Assess risk level
      comparison.riskLevel = this.assessRiskLevel(comparison);
      
      // Generate changes summary
      comparison.changesSummary = this.summarizeChanges(comparison);
      
      return comparison;
      
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }
  
  /**
   * Generate comprehensive merge report
   * @param {object} existing - Existing document
   * @param {object} template - New template
   * @param {object} strategy - Update strategy
   * @param {object} options - Report options
   */
  async generateMergeReport(existing, template, strategy, options = {}) {
    try {
      const opts = {
        includePreview: options.includePreview !== false,
        includeRiskAnalysis: options.includeRiskAnalysis !== false,
        includeRecommendations: options.includeRecommendations !== false,
        includeBackupPlan: options.includeBackupPlan !== false,
        format: options.format || 'detailed', // 'summary', 'detailed', 'technical'
        ...options
      };
      
      const report = {
        timestamp: Date.now(),
        executiveeSummary: {},
        documentAnalysis: {},
        updateStrategy: {},
        riskAssessment: {},
        recommendations: [],
        backupPlan: {},
        preview: null,
        metadata: {
          analyzerVersion: '1.0.0',
          projectPath: existing.projectPath,
          documentPath: existing.path,
          reportFormat: opts.format
        }
      };
      
      // Executive Summary
      report.executiveSummary = {
        documentType: existing.type,
        documentSize: existing.content?.length || 0,
        customizationsFound: existing.customizations?.length || 0,
        updateComplexity: strategy.complexity || 'medium',
        riskLevel: strategy.riskLevel || 'medium',
        recommendedAction: strategy.recommendedAction || 'proceed-with-caution',
        estimatedTime: strategy.estimatedTime || 'unknown'
      };
      
      // Document Analysis
      report.documentAnalysis = {
        existingDocument: {
          path: existing.path,
          type: existing.type,
          size: existing.content?.length || 0,
          lastModified: existing.lastModified,
          isAgentOSManaged: existing.isAgentOSManaged || false,
          hasCustomizations: (existing.customizations?.length || 0) > 0,
          customizations: existing.customizations || []
        },
        newTemplate: {
          version: template.version,
          changes: template.changes || [],
          newFeatures: template.newFeatures || [],
          deprecations: template.deprecations || []
        }
      };
      
      // Update Strategy Details
      report.updateStrategy = {
        strategy: strategy.strategy,
        steps: strategy.steps || [],
        preservationPlan: strategy.preservationPlan || {},
        mergeConflicts: strategy.conflicts || [],
        conflictResolution: strategy.conflictResolution || []
      };
      
      // Risk Assessment
      if (opts.includeRiskAnalysis) {
        report.riskAssessment = await this.generateRiskAssessment(
          existing, 
          template, 
          strategy
        );
      }
      
      // Recommendations
      if (opts.includeRecommendations) {
        report.recommendations = await this.generateRecommendations(
          existing,
          template,
          strategy,
          report
        );
      }
      
      // Backup Plan
      if (opts.includeBackupPlan) {
        report.backupPlan = await this.generateBackupPlan(existing, strategy);
      }
      
      // Preview
      if (opts.includePreview) {
        report.preview = await this.generateMergePreview(existing, template, strategy);
      }
      
      // Store report
      const reportKey = this.generateReportKey(existing.path, template, strategy);
      this.mergeReports.set(reportKey, report);
      
      this.emit('merge-report-generated', {
        document: existing.path,
        reportKey,
        riskLevel: report.executiveSummary.riskLevel,
        recommendations: report.recommendations.length
      });
      
      return report;
      
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }
  
  /**
   * Discover Agent-OS documents in a project
   */
  async discoverAgentOSDocuments(projectPath, options = {}) {
    const documents = [];
    
    try {
      const entries = await fs.readdir(projectPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(projectPath, entry.name);
        
        if (entry.isDirectory()) {
          // Skip certain directories
          if (this.shouldSkipDirectory(entry.name)) {
            continue;
          }
          
          // Recurse if enabled
          if (options.recursive) {
            const subDocs = await this.discoverAgentOSDocuments(fullPath, options);
            documents.push(...subDocs);
          }
        } else if (entry.isFile()) {
          // Check if file matches Agent-OS patterns
          if (await this.isAgentOSDocument(fullPath, entry.name)) {
            documents.push(fullPath);
          }
        }
      }
      
    } catch (error) {
      console.warn(`Failed to read directory ${projectPath}:`, error.message);
    }
    
    return documents;
  }
  
  /**
   * Check if a file is an Agent-OS document
   */
  async isAgentOSDocument(filePath, fileName) {
    try {
      // Check file name patterns
      const matchesPattern = this.agentOSPatterns.filePatterns.some(pattern => 
        pattern.test(fileName)
      );
      
      if (matchesPattern) {
        return true;
      }
      
      // Check content signatures for other files
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        const hasSignature = this.agentOSPatterns.contentSignatures.some(signature =>
          signature.test(content)
        );
        
        return hasSignature;
      } catch (error) {
        // If we can't read the file, assume it's not an Agent-OS document
        return false;
      }
      
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Analyze a single document
   */
  async analyzeDocument(docPath, options = {}) {
    try {
      const content = await fs.readFile(docPath, 'utf-8');
      const stats = await fs.stat(docPath);
      const fileName = path.basename(docPath);
      const extension = path.extname(docPath).slice(1).toLowerCase();
      
      // Determine document type
      const docType = this.detectDocumentType(fileName, extension, content);
      
      // Basic document information
      const document = {
        path: docPath,
        fileName,
        extension,
        type: docType,
        content,
        size: content.length,
        lastModified: stats.mtime,
        createdAt: stats.birthtime || stats.mtime,
        isAgentOSManaged: false,
        agentOSVersion: null,
        templateVersion: null,
        customizations: [],
        structure: null,
        metadata: {},
        analysis: {
          complexity: 'unknown',
          customizationLevel: 'unknown',
          updateRisk: 'unknown'
        }
      };
      
      // Use specific analyzer based on document type
      const analyzer = this.analyzers[docType] || this.analyzers['text'];
      const analysisResult = await analyzer(document, options);
      
      // Merge analysis results
      Object.assign(document, analysisResult);
      
      // Detect customizations if enabled
      if (options.detectCustomizations) {
        await this.detectCustomizations(document, options);
      }
      
      // Cache result if enabled
      if (options.cacheResults) {
        this.documentCache.set(docPath, document);
      }
      
      return document;
      
    } catch (error) {
      throw new Error(`Failed to analyze document ${docPath}: ${error.message}`);
    }
  }
  
  /**
   * Detect document type from various indicators
   */
  detectDocumentType(fileName, extension, content) {
    // Check extension first
    const typeByExtension = {
      'md': 'markdown',
      'json': 'json',
      'yml': 'yaml',
      'yaml': 'yaml',
      'js': 'javascript',
      'ts': 'javascript',
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'dockerfile': 'dockerfile',
      'txt': 'text'
    };
    
    if (typeByExtension[extension]) {
      return typeByExtension[extension];
    }
    
    // Check file name patterns
    if (/dockerfile/i.test(fileName)) {
      return 'dockerfile';
    }
    
    if (/docker-compose|compose/i.test(fileName)) {
      return 'yaml';
    }
    
    // Check content patterns
    if (content.includes('#!/bin/bash') || content.includes('#!/bin/sh')) {
      return 'shell';
    }
    
    if (content.trim().startsWith('{') && content.trim().endsWith('}')) {
      return 'json';
    }
    
    if (/^---\s*$/m.test(content) || /^\w+:\s*\w+/m.test(content)) {
      return 'yaml';
    }
    
    return 'text';
  }
  
  /**
   * Analyze Markdown documents
   */
  async analyzeMarkdownDocument(document, options = {}) {
    const analysis = {
      structure: this.parseMarkdownStructure(document.content),
      isAgentOSManaged: false,
      agentOSVersion: null,
      templateMarkers: [],
      customSections: []
    };
    
    // Check for Agent-OS management markers
    const agentOSMatch = document.content.match(/# Claude Configuration.*?(?=\n#|\n\n|$)/s);
    if (agentOSMatch) {
      analysis.isAgentOSManaged = true;
      
      // Extract version info
      const versionMatch = document.content.match(/Version[:\s]+([\d.]+)/i);
      if (versionMatch) {
        analysis.agentOSVersion = versionMatch[1];
      }
    }
    
    // Find template markers
    for (const pattern of this.agentOSPatterns.templateMarkers) {
      const matches = [...document.content.matchAll(pattern)];
      analysis.templateMarkers.push(...matches.map(match => ({
        marker: match[0],
        position: match.index,
        type: 'template-variable'
      })));
    }
    
    // Extract custom sections
    analysis.customSections = await this.extractCustomSections(document.content, {
      documentType: 'markdown'
    });
    
    return analysis;
  }
  
  /**
   * Analyze JSON documents
   */
  async analyzeJSONDocument(document, options = {}) {
    const analysis = {
      structure: null,
      isAgentOSManaged: false,
      agentOSVersion: null,
      configModifications: [],
      originalKeys: []
    };
    
    try {
      const parsed = JSON.parse(document.content);
      analysis.structure = this.analyzeJSONStructure(parsed);
      
      // Check for Agent-OS specific keys
      if (parsed['agent-os'] || parsed.agentOS || parsed.AGENT_OS_VERSION) {
        analysis.isAgentOSManaged = true;
        analysis.agentOSVersion = parsed['agent-os']?.version || 
                                 parsed.agentOS?.version ||
                                 parsed.AGENT_OS_VERSION;
      }
      
      // Check package.json specifics
      if (document.fileName === 'package.json') {
        analysis.packageAnalysis = await this.analyzePackageJSON(parsed);
      }
      
    } catch (error) {
      console.warn(`Failed to parse JSON in ${document.path}:`, error.message);
    }
    
    return analysis;
  }
  
  /**
   * Analyze YAML documents
   */
  async analyzeYAMLDocument(document, options = {}) {
    const analysis = {
      structure: this.parseYAMLStructure(document.content),
      isAgentOSManaged: false,
      agentOSVersion: null,
      services: [],
      volumes: [],
      networks: []
    };
    
    // Check for Agent-OS markers in comments
    const agentOSComments = document.content.match(/#.*Agent-OS.*version.*?([\d.]+)/i);
    if (agentOSComments) {
      analysis.isAgentOSManaged = true;
      analysis.agentOSVersion = agentOSComments[1];
    }
    
    // Analyze Docker Compose specifics
    if (/compose|docker-compose/i.test(document.fileName)) {
      analysis.dockerComposeAnalysis = await this.analyzeDockerCompose(document.content);
    }
    
    return analysis;
  }
  
  /**
   * Analyze JavaScript/TypeScript documents
   */
  async analyzeJavaScriptDocument(document, options = {}) {
    const analysis = {
      structure: this.parseJavaScriptStructure(document.content),
      isAgentOSManaged: false,
      agentOSVersion: null,
      imports: [],
      exports: [],
      classes: [],
      functions: []
    };
    
    // Check for Agent-OS markers in comments
    const agentOSComment = document.content.match(/\/\*.*Agent-OS.*version.*?([\d.]+).*?\*\//s);
    if (agentOSComment) {
      analysis.isAgentOSManaged = true;
      analysis.agentOSVersion = agentOSComment[1];
    }
    
    return analysis;
  }
  
  /**
   * Analyze Shell script documents
   */
  async analyzeShellDocument(document, options = {}) {
    const analysis = {
      structure: this.parseShellStructure(document.content),
      isAgentOSManaged: false,
      agentOSVersion: null,
      variables: [],
      functions: [],
      commands: []
    };
    
    // Check for Agent-OS markers in comments
    const agentOSComment = document.content.match(/#.*Agent-OS.*version.*?([\d.]+)/i);
    if (agentOSComment) {
      analysis.isAgentOSManaged = true;
      analysis.agentOSVersion = agentOSComment[1];
    }
    
    return analysis;
  }
  
  /**
   * Analyze Docker documents
   */
  async analyzeDockerDocument(document, options = {}) {
    const analysis = {
      structure: this.parseDockerStructure(document.content),
      isAgentOSManaged: false,
      agentOSVersion: null,
      baseImage: null,
      instructions: [],
      exposedPorts: [],
      volumes: []
    };
    
    // Check for Agent-OS markers in comments
    const agentOSComment = document.content.match(/#.*Agent-OS.*version.*?([\d.]+)/i);
    if (agentOSComment) {
      analysis.isAgentOSManaged = true;
      analysis.agentOSVersion = agentOSComment[1];
    }
    
    // Extract base image
    const fromMatch = document.content.match(/^FROM\s+(.+)$/m);
    if (fromMatch) {
      analysis.baseImage = fromMatch[1];
    }
    
    return analysis;
  }
  
  /**
   * Analyze text documents
   */
  async analyzeTextDocument(document, options = {}) {
    const analysis = {
      structure: this.parseTextStructure(document.content),
      isAgentOSManaged: false,
      agentOSVersion: null,
      lineCount: document.content.split('\n').length,
      wordCount: document.content.split(/\s+/).length,
      charCount: document.content.length
    };
    
    // Check for any Agent-OS signatures
    const hasSignature = this.agentOSPatterns.contentSignatures.some(signature =>
      signature.test(document.content)
    );
    
    if (hasSignature) {
      analysis.isAgentOSManaged = true;
    }
    
    return analysis;
  }
  
  /**
   * Helper methods for structure parsing
   */
  parseMarkdownStructure(content) {
    const structure = {
      headers: [],
      codeBlocks: [],
      lists: [],
      links: [],
      images: []
    };
    
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      // Headers
      const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);
      if (headerMatch) {
        structure.headers.push({
          level: headerMatch[1].length,
          text: headerMatch[2],
          line: index
        });
      }
      
      // Lists
      const listMatch = line.match(/^(\s*)[-*+]\s+(.+)$/);
      if (listMatch) {
        structure.lists.push({
          indent: listMatch[1].length,
          text: listMatch[2],
          line: index
        });
      }
      
      // Links
      const linkMatches = [...line.matchAll(/\[([^\]]+)\]\(([^)]+)\)/g)];
      linkMatches.forEach(match => {
        structure.links.push({
          text: match[1],
          url: match[2],
          line: index,
          position: match.index
        });
      });
    });
    
    return structure;
  }
  
  parseJavaScriptStructure(content) {
    const structure = {
      functions: [],
      classes: [],
      imports: [],
      exports: [],
      variables: []
    };
    
    // Simple regex-based parsing (could be enhanced with AST)
    const functionMatches = [...content.matchAll(/(?:function\s+(\w+)|(\w+)\s*=\s*(?:function|\(.*?\)\s*=>))/g)];
    functionMatches.forEach(match => {
      structure.functions.push({
        name: match[1] || match[2],
        position: match.index
      });
    });
    
    const classMatches = [...content.matchAll(/class\s+(\w+)/g)];
    classMatches.forEach(match => {
      structure.classes.push({
        name: match[1],
        position: match.index
      });
    });
    
    return structure;
  }
  
  parseShellStructure(content) {
    const structure = {
      functions: [],
      variables: [],
      commands: []
    };
    
    // Function definitions
    const functionMatches = [...content.matchAll(/(\w+)\(\)\s*\{/g)];
    functionMatches.forEach(match => {
      structure.functions.push({
        name: match[1],
        position: match.index
      });
    });
    
    // Variable assignments
    const variableMatches = [...content.matchAll(/^(\w+)=/gm)];
    variableMatches.forEach(match => {
      structure.variables.push({
        name: match[1],
        position: match.index
      });
    });
    
    return structure;
  }
  
  parseDockerStructure(content) {
    const structure = {
      instructions: [],
      stages: []
    };
    
    const lines = content.split('\n');
    lines.forEach((line, index) => {
      const instructionMatch = line.match(/^(\w+)\s+(.+)$/);
      if (instructionMatch) {
        structure.instructions.push({
          instruction: instructionMatch[1],
          args: instructionMatch[2],
          line: index
        });
      }
    });
    
    return structure;
  }
  
  parseYAMLStructure(content) {
    const structure = {
      keys: [],
      sections: []
    };
    
    const lines = content.split('\n');
    lines.forEach((line, index) => {
      const keyMatch = line.match(/^(\s*)([^:]+):\s*(.*)?$/);
      if (keyMatch) {
        structure.keys.push({
          indent: keyMatch[1].length,
          key: keyMatch[2].trim(),
          value: keyMatch[3],
          line: index
        });
      }
    });
    
    return structure;
  }
  
  parseTextStructure(content) {
    const lines = content.split('\n');
    return {
      lineCount: lines.length,
      nonEmptyLines: lines.filter(line => line.trim()).length,
      sections: []
    };
  }
  
  analyzeJSONStructure(obj) {
    const structure = {
      keys: Object.keys(obj),
      depth: this.calculateJSONDepth(obj),
      arrays: [],
      objects: []
    };
    
    for (const [key, value] of Object.entries(obj)) {
      if (Array.isArray(value)) {
        structure.arrays.push(key);
      } else if (typeof value === 'object' && value !== null) {
        structure.objects.push(key);
      }
    }
    
    return structure;
  }
  
  calculateJSONDepth(obj, depth = 0) {
    if (typeof obj !== 'object' || obj === null) {
      return depth;
    }
    
    let maxDepth = depth;
    for (const value of Object.values(obj)) {
      if (typeof value === 'object' && value !== null) {
        maxDepth = Math.max(maxDepth, this.calculateJSONDepth(value, depth + 1));
      }
    }
    
    return maxDepth;
  }
  
  /**
   * Additional helper methods
   */
  shouldSkipDirectory(dirName) {
    const skipDirs = [
      'node_modules',
      '.git',
      '.hive-mind',
      'dist',
      'build',
      'coverage',
      '.nyc_output',
      'logs',
      'tmp',
      'temp'
    ];
    
    return skipDirs.includes(dirName) || dirName.startsWith('.');
  }
  
  deduplicateCustomizations(customizations) {
    const seen = new Set();
    return customizations.filter(custom => {
      const key = `${custom.type}:${custom.position?.start}:${custom.content}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }
  
  rankCustomizations(customizations) {
    return customizations.sort((a, b) => {
      // Sort by confidence (higher first), then by position
      if (a.confidence !== b.confidence) {
        return b.confidence - a.confidence;
      }
      return (a.position?.start || 0) - (b.position?.start || 0);
    });
  }
  
  generateStrategyKey(docPath, template) {
    const content = `${docPath}:${template.version || 'unknown'}:${Date.now()}`;
    return crypto.createHash('md5').update(content).digest('hex').substring(0, 12);
  }
  
  generateReportKey(docPath, template, strategy) {
    const content = `${docPath}:${template.version}:${strategy.strategy}:${Date.now()}`;
    return crypto.createHash('md5').update(content).digest('hex').substring(0, 12);
  }
  
  async initializeTemplateRegistry() {
    // Initialize with common Agent-OS templates
    this.templateRegistry.set('claude-md', {
      version: '2.1.0',
      structure: 'markdown',
      sections: ['configuration', 'project-analysis', 'technology-stack']
    });
    
    this.templateRegistry.set('package-json', {
      version: '1.0.0',
      structure: 'json',
      requiredKeys: ['name', 'version', 'dependencies']
    });
  }
  
  async loadAnalysisCache() {
    if (this.sharedMemory) {
      try {
        const cacheKeys = await this.sharedMemory.keys({
          namespace: this.sharedMemory.namespaces.CACHE,
          pattern: 'document-analysis:*'
        });
        
        console.log(`Loaded ${cacheKeys.length} cached document analyses`);
      } catch (error) {
        console.warn('Failed to load analysis cache:', error.message);
      }
    }
  }
  
  /**
   * Placeholder methods for comprehensive functionality
   * These can be implemented as the system evolves
   */
  
  async getOriginalTemplate(doc) {
    // Placeholder: Retrieve original template for comparison
    return null;
  }
  
  async detectAgentOSPatternCustomizations(doc) {
    // Placeholder: Detect Agent-OS specific customization patterns
    return [];
  }
  
  async detectStructuralCustomizations(doc) {
    // Placeholder: Detect structural modifications
    return [];
  }
  
  async detectSemanticCustomizations(doc) {
    // Placeholder: Detect semantic changes in content
    return [];
  }
  
  async detectMergeConflicts(existing, newTemplate, differences) {
    // Placeholder: Detect potential merge conflicts
    return [];
  }
  
  async calculatePreserveUserStrategy(existing, newTemplate, options) {
    // Placeholder: Calculate preservation-focused strategy
    return {
      strategy: 'preserve-user',
      steps: [],
      riskLevel: 'low',
      estimatedTime: '5-10 minutes'
    };
  }
  
  async calculateIntelligentMergeStrategy(existing, newTemplate, options) {
    // Placeholder: Calculate intelligent merge strategy
    return {
      strategy: 'merge-intelligent',
      steps: [],
      riskLevel: 'medium',
      estimatedTime: '10-15 minutes'
    };
  }
  
  async calculateSafeOverwriteStrategy(existing, newTemplate, options) {
    // Placeholder: Calculate safe overwrite strategy
    return {
      strategy: 'overwrite-safe',
      steps: [],
      riskLevel: 'high',
      estimatedTime: '2-5 minutes'
    };
  }
  
  async calculateVersionControlStrategy(existing, newTemplate, options) {
    // Placeholder: Calculate version control strategy
    return {
      strategy: 'version-control',
      steps: [],
      riskLevel: 'low',
      estimatedTime: '15-20 minutes'
    };
  }
  
  async calculateIncrementalStrategy(existing, newTemplate, options) {
    // Placeholder: Calculate incremental update strategy
    return {
      strategy: 'incremental',
      steps: [],
      riskLevel: 'low',
      estimatedTime: '5-10 minutes'
    };
  }
  
  async generateProjectAnalysis(projectPath, analysisResults, options) {
    // Placeholder: Generate comprehensive project-level analysis
    return {
      projectPath,
      totalDocuments: analysisResults.size,
      agentOSDocuments: Array.from(analysisResults.values()).filter(doc => doc.isAgentOSManaged).length,
      documentsWithCustomizations: Array.from(analysisResults.values()).filter(doc => doc.customizations?.length > 0).length,
      analysisResults: Object.fromEntries(analysisResults),
      recommendations: []
    };
  }
  
  async cacheAnalysisResults(projectPath, analysis) {
    if (this.sharedMemory) {
      const key = `document-analysis:${crypto.createHash('md5').update(projectPath).digest('hex')}`;
      await this.sharedMemory.set(key, analysis, {
        namespace: this.sharedMemory.namespaces.CACHE,
        dataType: this.sharedMemory.dataTypes.CACHED,
        ttl: 86400000 // 24 hours
      });
    }
  }
  
  // Additional placeholder methods for completeness
  extractMarkedCustomSections(content) { return []; }
  extractUserComments(content) { return []; }
  extractUserAdditions(content) { return []; }
  async detectImplicitCustomSections(content) { return []; }
  async preserveCustomization(customization, template, options) { return null; }
  async findInsertionPoints(template, customizations) { return []; }
  async applyPreservationStrategy(template, strategy, options) { return ''; }
  async compareContent(content1, content2, options) { return []; }
  async compareStructure(struct1, struct2, options) { return []; }
  async compareMetadata(meta1, meta2, options) { return []; }
  calculateSimilarity(comparison) { return 0.5; }
  assessRiskLevel(comparison) { return 'medium'; }
  summarizeChanges(comparison) { return {}; }
  async generateRiskAssessment(existing, template, strategy) { return {}; }
  async generateRecommendations(existing, template, strategy, report) { return []; }
  async generateBackupPlan(existing, strategy) { return {}; }
  async generateMergePreview(existing, template, strategy) { return null; }
  async analyzePackageJSON(parsed) { return {}; }
  async analyzeDockerCompose(content) { return {}; }
  async analyzeDocumentStructure(doc) { return {}; }
}

module.exports = AgentOSDocumentAnalyzer;