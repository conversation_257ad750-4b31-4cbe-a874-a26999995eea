{"name": "master-workflow", "version": "0.1.0", "private": true, "description": "Intelligent Workflow Decision System - governance and CI scaffolding", "scripts": {"test": "node test/test-runner.js", "lint": "node scripts/lint-if-available.js", "coverage": "echo 'No coverage tool configured; skipping'", "install-sqlite": "npm install sqlite3 || echo 'SQLite3 installation failed, will use file-based fallback'"}, "engines": {"node": ">=18"}, "optionalDependencies": {"sqlite3": "^5.1.7"}, "dependencies": {"@babel/parser": "^7.28.0", "archiver": "^7.0.1", "chalk": "^4.1.2", "cli-table3": "^0.6.5", "diff": "^8.0.2", "inquirer": "^9.3.7", "tar": "^7.4.3"}}