# MASTER-WORKFLOW Documentation

This directory contains comprehensive documentation for the MASTER-WORKFLOW v3.0 system.

## Guides

### [Queen Controller Guide](./guides/QUEEN-CONTROLLER-GUIDE.md)
Complete reference for the Queen Controller - the central orchestration system managing 10 concurrent sub-agents with neural learning integration.

**Features Covered:**
- 🤖 **10 Concurrent Sub-Agents**: Complete agent management system
- 🧠 **Neural Learning Integration**: AI-powered task optimization  
- 📊 **Performance Metrics**: Production benchmarks and monitoring
- 🔄 **Event-Driven Architecture**: Real-time coordination patterns
- 🛠️ **CLI Commands**: Complete command reference
- 🚨 **Error Handling**: Troubleshooting and recovery patterns
- 💡 **Best Practices**: Production deployment guidance

## Architecture Documentation

For system architecture and implementation details, see:
- `/intelligence-engine/README.md` - Core intelligence engine
- `/intelligence-engine/NEURAL-LEARNING-SYSTEM.md` - Neural learning system
- `/intelligence-engine/SHARED-MEMORY-README.md` - Shared memory architecture
- `/END-OF-PHASE-SUMMARIES/PHASE-SIX/` - Latest implementation status

## Agent Documentation

For sub-agent specifications and templates:
- `/sub-agent-documentation/` - Agent creation and management
- `/agent-templates/` - Specialized agent templates
- `/generated-agents/` - Auto-generated agent configurations

## Quick Links

- **[Queen Controller API Reference](./guides/QUEEN-CONTROLLER-GUIDE.md#api-reference)** - Complete method documentation
- **[Agent Types](./guides/QUEEN-CONTROLLER-GUIDE.md#agent-types)** - 10 specialized agent types
- **[Performance Metrics](./guides/QUEEN-CONTROLLER-GUIDE.md#performance-metrics)** - Benchmarks and monitoring
- **[CLI Commands](./guides/QUEEN-CONTROLLER-GUIDE.md#cli-commands)** - Command line interface
- **[Troubleshooting](./guides/QUEEN-CONTROLLER-GUIDE.md#troubleshooting)** - Common issues and solutions

---

*Documentation generated by Documentation Specialist Sub-Agent*  
*Part of MASTER-WORKFLOW v3.0 with Neural Learning Integration*