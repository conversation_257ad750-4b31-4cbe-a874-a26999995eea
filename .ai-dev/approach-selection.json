{"selected": "hiveMind", "name": "Hive-Mind", "command": "npx claude-flow@alpha hive-mind spawn --agents 4 --claude \"MASTER-WORKFLOW\"", "commands": ["npx claude-flow@alpha hive-mind spawn \"MASTER-WORKFLOW\" --agents 4 --claude"], "score": 39, "stage": "active", "description": "Intelligent multi-agent coordination with specialized roles", "timeEstimate": "30 minutes - 4 hours", "agentCount": "4-6", "bestFor": ["Multi-feature development", "Fullstack applications", "Complex integrations", "Team-based development", "Medium-scale projects"], "reasoning": ["Medium complexity score (39/100) requires coordinated approach", "Active development requires efficient feature delivery"], "matchScore": 88, "alternatives": [{"name": "Simple Swarm", "matchScore": 82, "command": "npx claude-flow swarm"}, {"name": "Hive-Mind + SPARC", "matchScore": 36, "command": "npx claude-flow hive-mind spawn --sparc"}], "setupSteps": ["Initialize AI Dev OS environment", "Analyze project complexity", "Spawn Hive-Mind coordination system", "Configure specialized agent roles", "Set up cross-session memory", "Initialize parallel execution environment"], "userSelected": true}