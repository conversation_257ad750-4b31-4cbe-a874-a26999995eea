{"score": 57, "factors": {"size": {"fileCount": 171, "score": 50, "description": "Medium project"}, "dependencies": {"count": 7, "score": 30, "description": "7 dependencies detected"}, "architecture": {"patterns": {"microservices": 20, "monolith": 0, "frontend": 0, "backend": 50, "fullstack": 0, "api": 30, "database": 20}, "score": 50, "primaryArchitecture": "backend"}, "techStack": {"languages": ["JavaScript", "TypeScript", "Python", "Go", "Java", "Rust"], "frameworks": [], "databases": ["SQLite"], "tools": [], "containerized": true, "aiTools": ["<PERSON>", "Agent-OS", "<PERSON>", "TMux Orchestrator", "OpenAI"], "score": 100, "diversity": 12}, "features": {"detected": {"authentication": false, "realtime": false, "api": false, "database": false, "testing": true, "ci_cd": true, "docker": true, "kubernetes": true}, "count": 4, "score": 60}, "team": {"indicators": {"multiContributor": true, "documentation": true, "codeReviews": true, "issueTracking": false}, "score": 30, "isTeamProject": true}, "deployment": {"docker": true, "kubernetes": false, "cicd": true, "cloudProvider": null, "monitoring": true, "score": 55}, "testing": {"hasTests": true, "testFileCount": 100, "testTypes": {"unit": false, "integration": true, "e2e": true}, "score": 85}}, "stage": "active", "recommendations": [{"approach": "Hive-Mind", "reason": "Medium complexity requiring multi-agent coordination", "confidence": 0.85}, {"focus": "Feature Development", "suggestion": "Maintain consistency while adding features"}], "confidence": 100}