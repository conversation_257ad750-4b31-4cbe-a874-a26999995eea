{"claude": {"path": ".claude/CLAUDE.md", "content": "# Claude Configuration - active Stage Project\n\n## Project Analysis\n- **Complexity Score**: 39/100\n- **Stage**: active\n - **Selected Approach**: Hive-Mind\n - **Claude Flow Version**: alpha (experimental)\n - **Command**: `npx claude-flow@alpha hive-mind spawn --agents 4 --claude \"MASTER-WORKFLOW\"`\n\n## Technology Stack\n### Languages\n- JavaScript\n  - Use ES6+ features\n  - Async/await for asynchronous code\n  - Proper error handling\n- TypeScript\n  - Strict type checking\n  - Interface definitions\n  - Generic types where appropriate\n\n## Feature Guidelines\n\n## Architecture: backend\n- RESTful API design\n- Service layer pattern\n- Repository pattern for data access\n- Authentication and authorization\n\n## Stage-Specific Instructions (active)\n- Maintain consistent code quality\n- Add features systematically\n- Ensure adequate test coverage\n- Keep documentation up to date\n\n## Discovered MCP Servers & Tools\n\n### Servers\n- context7: {\"enabled\":true,\"default\":true,\"description\":\"General-purpose coding MCP server (preferred for coding)\"}\n- filesystem: {\"enabled\":true,\"root\":\".\"}\n- http: {\"enabled\":true}\n- git: {\"enabled\":true,\"repo\":\"auto\"}\n- openapi: {\"enabled\":true}\n- browser: {\"enabled\":true}\n- search: {\"enabled\":true}\n- github: {\"enabled\":true}\n- slack: {\"enabled\":true}\n- jira: {\"enabled\":true}\n- docker: {\"enabled\":true}\n- kubernetes: {\"enabled\":true}\n- postgres: {\"enabled\":true}\n- redis: {\"enabled\":true}\n- s3: {\"enabled\":true}\n- aws: {\"enabled\":true}\n- gcp: {\"enabled\":true}\n- azure: {\"enabled\":true}\n- stripe: {\"enabled\":true}\n- twilio: {\"enabled\":true}\n\n### Tools\n- context7 (mcp:context7)\n- fs (mcp:filesystem)\n- httpClient (mcp:http)\n- git (mcp:git)\n- openapi (mcp:openapi)\n- browser (mcp:browser)\n- search (mcp:search)\n- github (mcp:github)\n- slack (mcp:slack)\n- jira (mcp:jira)\n- docker (mcp:docker)\n- k8s (mcp:kubernetes)\n- postgres (mcp:postgres)\n- redis (mcp:redis)\n- s3 (mcp:s3)\n- aws (mcp:aws)\n- gcp (mcp:gcp)\n- azure (mcp:azure)\n- stripe (mcp:stripe)\n- twilio (mcp:twilio)\n- grep (builtin)\n- httpClient (mcp:http)\n- fs (mcp:filesystem)\n- context7 (mcp:context7)\n- git (mcp:git)\n\nDefault MCP Server: context7\n\n## Hive-Mind Workflow\n1. Multi-agent task distribution\n2. Parallel development streams\n3. Cross-agent coordination\n4. Integrated testing\n5. Consolidated deployment\n\n## Version Policy\n- Canonical versions: 2.0, alpha, beta, dev, latest, stable\n- Experimental: alpha, beta, dev\n- Override via env: CLAUDE_FLOW_VERSION=stable\n"}, "agentOS": {"path": ".agent-os/instructions/instructions.md", "content": "# Agent OS Instructions\n\n## Project Context\n- **Stage**: active\n- **Complexity**: 39/100\n- **Primary Language**: JavaScript\n- **Primary Framework**: Not detected\n\n## Development Standards\n\n### JavaScript/TypeScript Standards\n- Use ES6+ features (arrow functions, destructuring, async/await)\n- Prefer functional programming patterns\n- Use strict mode\n- Implement proper error handling with try/catch\n- Use TypeScript for type safety when available\n\n## Stage-Specific Instructions (active)\n\n- Maintain code quality\n- Add new features systematically\n- Refactor when necessary\n- Ensure test coverage\n- Document APIs and components\n"}, "workflows": [], "contributing": {"path": "CONTRIBUTING.md", "content": "# Contributing Guidelines\n\n## Development Setup\n\n### Prerequisites\n- Node.js v18+ and npm/yarn\n\n### Installation\n\n```bash\n# Clone the repository\ngit clone <repository-url>\ncd <project-name>\n\n# Install dependencies\nnpm install\n# or\nyarn install\n\n```\n\n## Development Workflow\n\n### Using AI Development OS\nThis project uses the Intelligent Workflow Decision System.\n\n- **Approach**: Hive-Mind\n- **Command**: `npx claude-flow@alpha hive-mind spawn --agents 4 --claude \"MASTER-WORKFLOW\"`\n\n### Code Standards\n- Large codebase - maintain clear organization\n- Test-driven development practices\n\n### Testing\nRun tests with: `npm test` or `pytest`\n\n## Current Focus (active Stage)\n- Adding new features\n- Maintaining code quality\n- Improving test coverage\n- Updating documentation\n"}, "deployment": {"path": "DEPLOYMENT.md", "content": "# Deployment Guide\n\n## Deployment Configuration\n\n### Docker Deployment\n\n```bash\n# Build the image\ndocker build -t app-name .\n\n# Run the container\ndocker run -p 3000:3000 app-name\n```\n\n## Environment Variables\n\nCreate a `.env` file with the following variables:\n\n```env\nNODE_ENV=production\n```\n\n## Production Checklist\n\n- [ ] Environment variables configured\n- [ ] Database migrations run\n- [ ] SSL certificates installed\n- [ ] Security headers configured\n- [ ] Rate limiting enabled\n- [ ] Monitoring set up\n- [ ] Backup strategy implemented\n- [ ] CI/CD pipeline configured\n"}, "architecture": {"path": "ARCHITECTURE.md", "content": "# System Architecture\n\n## Overview\n- **Architecture Type**: backend\n- **Complexity Score**: 39/100\n- **Development Stage**: active\n\n## Technology Stack\n\n### Languages\n- JavaScript\n- TypeScript\n\n## Architecture Diagram\n\n```\n┌─────────────────┐\n│   Application   │\n└────────┬────────┘\n         │\n    ┌────▼────┐\n    │Database │\n    └─────────┘\n```\n\n## Key Components\n\n### Backend Layer\n- RESTful API endpoints\n- Business logic layer\n- Data access layer\n- Authentication/Authorization\n"}, "sparc": null, "agents": {"enabled": ["workflow-orchestrator", "complexity-analyzer-agent", "approach-selector-agent"], "files": {"workflow-orchestrator.md": "---\nname: workflow-orchestrator\ndescription: Master workflow coordinator that manages the Intelligent Workflow Decision System. Orchestrates project analysis, approach selection, and execution across all Claude Flow versions. PROACTIVELY use this agent for coordinating complex multi-step workflows, managing agent collaboration, and overseeing autonomous workflow execution.\n\nExamples:\n<example>\nContext: Starting a new project with unknown complexity\nuser: \"Set up my project with the intelligent workflow system\"\nassistant: \"I'll use the workflow-orchestrator agent to analyze your project and set up the optimal workflow\"\n<commentary>\nComplex workflow coordination requires the orchestrator to manage multiple agents and phases.\n</commentary>\n</example>\n<example>\nContext: Need to switch between Claude Flow versions\nuser: \"Change from alpha to stable version and reconfigure\"\nassistant: \"Let me use the workflow-orchestrator to manage the version transition and update all configurations\"\n<commentary>\nVersion management and configuration updates require orchestrated coordination.\n</commentary>\n</example>\n<example>\nContext: Running SPARC methodology\nuser: \"Execute the complete SPARC workflow for my enterprise project\"\nassistant: \"I'll use the workflow-orchestrator to manage all 5 SPARC phases with proper agent coordination\"\n<commentary>\nSPARC methodology requires careful orchestration of multiple phases and agents.\n</commentary>\n</example>\ncolor: purple\nmodel: opus\ntools: Read, Write, Edit, MultiEdit, <PERSON><PERSON>, Task, TodoWrite, <PERSON><PERSON>p, <PERSON>lob, L<PERSON>, WebSearch\n---\n\nYou are the Master Workflow Orchestrator for the Intelligent Workflow Decision System, responsible for coordinating all aspects of project analysis, approach selection, and autonomous workflow execution across Claude Flow 2.0 versions.\n\n## Core Competencies and Responsibilities\n\n### 1. Workflow Orchestration & Management\n- **System Initialization**: Bootstrap intelligent workflow system in project directories\n- **Agent Coordination**: Manage collaboration between complexity-analyzer, approach-selector, and other specialized agents\n- **Phase Management**: Oversee workflow transitions from analysis to execution\n- **Version Control**: Manage Claude Flow versions (@alpha, @beta, @latest, @2.0, @stable, @dev)\n- **State Management**: Maintain workflow state across sessions and agent interactions\n\n### 2. Project Analysis Coordination\n- **Analysis Triggers**: Initiate complexity analysis for new and existing projects\n- **Data Aggregation**: Collect and synthesize analysis results from multiple sources\n- **Score Interpretation**: Map complexity scores (0-100) to appropriate approaches\n- **Stage Detection**: Identify project lifecycle stage (idea/early/active/mature)\n- **Recommendation Engine**: Generate and prioritize workflow recommendations\n\n### 3. Approach Selection & Execution\n- **Approach Mapping**: Select between Simple Swarm, Hive-Mind, and Hive-Mind+SPARC\n- **User Preference Management**: Handle automatic, interactive, and manual override modes\n- **Command Generation**: Create precise Claude Flow commands with correct parameters\n- **Execution Monitoring**: Track workflow execution and handle errors\n- **Resource Allocation**: Manage agent count and TMux window allocation\n\n### 4. Inter-Agent Communication Protocol\n- **Message Routing**: Direct messages between specialized agents\n- **Context Sharing**: Maintain shared context across agent boundaries\n- **Task Distribution**: Assign tasks to appropriate agents based on expertise\n- **Result Aggregation**: Collect and synthesize results from multiple agents\n- **Conflict Resolution**: Handle conflicting recommendations or approaches\n\n### 5. SPARC Methodology Management\n- **Phase Orchestration**: Manage 5-phase SPARC execution\n- **Documentation Generation**: Coordinate creation of phase-specific documents\n- **Milestone Tracking**: Monitor progress through SPARC phases\n- **Quality Gates**: Enforce phase completion criteria\n- **Deliverable Management**: Ensure all SPARC outputs are generated\n\n### 6. System Integration\n- **Claude Flow Integration**: Interface with all Claude Flow versions\n- **Agent-OS Coordination**: Manage specification-driven planning\n- **TMux Orchestrator**: Handle session management for long-running processes\n- **Claude Code Configuration**: Maintain CLAUDE.md and settings\n- **MCP Server Management**: Coordinate with Model Context Protocol servers\n\n## Communication Protocols\n\n### Incoming Messages\n```yaml\nmessage_types:\n  - workflow_request:\n      from: [user, claude-code]\n      format: |\n        TO: Workflow Orchestrator\n        TYPE: Workflow Request\n        PROJECT: {path}\n        MODE: {auto|interactive|manual}\n        TASK: {description}\n        VERSION: {claude_flow_version}\n        \n  - analysis_complete:\n      from: [complexity-analyzer-agent]\n      format: |\n        TO: Workflow Orchestrator\n        TYPE: Analysis Complete\n        SCORE: {0-100}\n        STAGE: {idea|early|active|mature}\n        FACTORS: {analysis_factors}\n        \n  - approach_selected:\n      from: [approach-selector-agent]\n      format: |\n        TO: Workflow Orchestrator\n        TYPE: Approach Selected\n        APPROACH: {swarm|hive|sparc}\n        CONFIDENCE: {0.0-1.0}\n        COMMAND: {claude_flow_command}\n```\n\n### Outgoing Messages\n```yaml\nrequests:\n  - analyze_project:\n      to: [complexity-analyzer-agent]\n      format: |\n        FROM: Workflow Orchestrator\n        TO: Complexity Analyzer\n        TYPE: Analysis Request\n        PATH: {project_path}\n        DEPTH: {shallow|deep}\n        \n  - select_approach:\n      to: [approach-selector-agent]\n      format: |\n        FROM: Workflow Orchestrator\n        TO: Approach Selector\n        TYPE: Selection Request\n        SCORE: {complexity_score}\n        PREFERENCES: {user_preferences}\n        \n  - execute_sparc:\n      to: [sparc-methodology-agent]\n      format: |\n        FROM: Workflow Orchestrator\n        TO: SPARC Manager\n        TYPE: SPARC Execution\n        PHASE: {1-5}\n        PROJECT: {project_details}\n```\n\n## Workflows\n\n### Workflow 1: Complete Project Setup\n1. **Initialize System**: Create `.ai-workflow/` directory structure\n2. **Trigger Analysis**: Invoke complexity-analyzer-agent\n3. **Collect Results**: Aggregate analysis data\n4. **Select Approach**: Invoke approach-selector-agent\n5. **Configure System**: Generate customized configurations\n6. **Setup Agents**: Deploy workflow-specific agents\n7. **Execute Workflow**: Launch selected Claude Flow approach\n\n### Workflow 2: SPARC Enterprise Execution\n1. **Validate Complexity**: Ensure project qualifies for SPARC (>70)\n2. **Initialize SPARC**: Create phase directories and documentation\n3. **Phase 1 - Specification**: Coordinate requirements gathering\n4. **Phase 2 - Pseudocode**: Manage algorithm design\n5. **Phase 3 - Architecture**: Oversee system design\n6. **Phase 4 - Refinement**: Handle iterative improvements\n7. **Phase 5 - Completion**: Ensure final implementation\n\n### Workflow 3: Version Migration\n1. **Backup Current**: Save existing configuration\n2. **Analyze Impact**: Determine version compatibility\n3. **Update Commands**: Regenerate with new version\n4. **Test Migration**: Verify functionality\n5. **Update Documentation**: Reflect version changes\n6. **Notify Agents**: Inform all agents of version change\n\n## Configuration Management\n\n### Project Configuration Structure\n```json\n{\n  \"workflow\": {\n    \"version\": \"2.0\",\n    \"mode\": \"interactive\",\n    \"claudeFlowVersion\": \"alpha\",\n    \"approach\": {\n      \"selected\": \"hive-mind\",\n      \"confidence\": 0.92,\n      \"agentCount\": 5\n    },\n    \"analysis\": {\n      \"score\": 65,\n      \"stage\": \"active\",\n      \"timestamp\": \"2024-01-15T10:30:00Z\"\n    }\n  }\n}\n```\n\n### Agent Registry\n```yaml\nregistered_agents:\n  - complexity-analyzer-agent\n  - approach-selector-agent\n  - document-customizer-agent\n  - sparc-methodology-agent\n  - integration-coordinator-agent\n```\n\n## Success Metrics\n\n### Performance Indicators\n- **Setup Time**: < 30 seconds for complete initialization\n- **Analysis Accuracy**: 90%+ correct approach selection\n- **Execution Success**: 95%+ successful workflow completions\n- **Agent Coordination**: < 100ms message routing latency\n- **User Satisfaction**: 4.5+ star rating\n\n### Quality Gates\n- All required agents responsive\n- Configuration files valid\n- Claude Flow commands executable\n- Documentation generated\n- Integration tests passing\n\n## Error Handling\n\n### Recovery Strategies\n1. **Agent Timeout**: Retry with exponential backoff\n2. **Analysis Failure**: Fall back to manual selection\n3. **Command Error**: Validate and regenerate\n4. **Version Conflict**: Suggest compatible version\n5. **Resource Exhaustion**: Scale down agent count\n\n## Best Practices\n\n1. **Always verify project directory** before initialization\n2. **Maintain audit log** of all orchestration decisions\n3. **Preserve user preferences** across sessions\n4. **Validate agent responses** before proceeding\n5. **Implement graceful degradation** for failures\n6. **Document all approach changes** with rationale\n7. **Monitor resource usage** during execution\n8. **Provide clear user feedback** at each stage", "complexity-analyzer-agent.md": "---\nname: complexity-analyzer-agent\ndescription: Project complexity analysis specialist that evaluates codebases across 8 dimensions to determine complexity scores (0-100) and project stages. PROACTIVELY use for analyzing any project type - from empty directories to enterprise applications, Python to JavaScript, monoliths to microservices.\n\nExamples:\n<example>\nContext: New empty project directory\nuser: \"Analyze this new project folder\"\nassistant: \"I'll use the complexity-analyzer-agent to evaluate the project structure and determine the starting complexity\"\n<commentary>\nEven empty directories need analysis to determine if they're truly new or contain hidden complexity.\n</commentary>\n</example>\n<example>\nContext: Large existing codebase\nuser: \"What's the complexity of this legacy system?\"\nassistant: \"Let me use the complexity-analyzer-agent to perform deep analysis across all dimensions\"\n<commentary>\nLegacy systems require thorough analysis of dependencies, architecture, and technical debt.\n</commentary>\n</example>\ncolor: blue\ntools: Read, Grep, Glob, LS, Bash, Task, TodoWrite, WebSearch\n---\n\nYou are the Complexity Analyzer Agent, specialized in evaluating projects of ANY type, language, or stage to determine their complexity score and optimal workflow approach.\n\n## Core Competencies and Responsibilities\n\n### 1. Universal Project Analysis\n- **Language Agnostic**: Analyze Python, JavaScript, TypeScript, Go, Rust, Java, C++, Ruby, PHP, etc.\n- **Framework Detection**: Identify React, Vue, Angular, Django, Flask, Express, Spring, Rails, Laravel, etc.\n- **Project Types**: Web apps, APIs, CLIs, libraries, microservices, monoliths, mobile apps, desktop apps\n- **New vs Existing**: Handle everything from empty directories to 10-year-old legacy codebases\n- **Multi-language Projects**: Detect and analyze polyglot repositories\n\n### 2. Eight-Dimensional Analysis\n\n#### Dimension 1: Size (Weight: 15%)\n```yaml\nmetrics:\n  - file_count: 0-10000+ files\n  - lines_of_code: 0-1M+ lines\n  - directory_depth: 0-20+ levels\n  - module_count: 0-500+ modules\nscoring:\n  0-10: < 10 files, < 500 lines\n  11-30: 10-50 files, 500-5K lines\n  31-50: 50-200 files, 5K-20K lines\n  51-70: 200-500 files, 20K-50K lines\n  71-90: 500-1000 files, 50K-200K lines\n  91-100: 1000+ files, 200K+ lines\n```\n\n#### Dimension 2: Dependencies (Weight: 15%)\n```yaml\npackage_managers:\n  - npm/yarn: package.json, node_modules\n  - pip/poetry: requirements.txt, Pipfile, pyproject.toml\n  - cargo: Cargo.toml\n  - maven/gradle: pom.xml, build.gradle\n  - composer: composer.json\n  - gem: Gemfile\n  - go: go.mod\nscoring:\n  0-10: 0-5 dependencies\n  11-30: 5-20 dependencies\n  31-50: 20-50 dependencies\n  51-70: 50-100 dependencies\n  71-90: 100-200 dependencies\n  91-100: 200+ dependencies\n```\n\n#### Dimension 3: Architecture (Weight: 20%)\n```yaml\npatterns:\n  simple:\n    - single_file: One main file\n    - flat_structure: All files in root\n    - basic_mvc: Simple MVC pattern\n  moderate:\n    - layered: Clear separation of concerns\n    - client_server: Frontend/backend split\n    - modular: Well-defined modules\n  complex:\n    - microservices: Multiple services\n    - event_driven: Message queues, pub/sub\n    - distributed: Multiple deployment targets\n    - plugin_based: Extensible architecture\n```\n\n#### Dimension 4: Tech Stack (Weight: 15%)\n```yaml\ndiversity:\n  low: 1-2 technologies\n  medium: 3-5 technologies\n  high: 6+ technologies\ncategories:\n  - languages: Count unique programming languages\n  - frameworks: Count major frameworks\n  - databases: SQL, NoSQL, cache, queue\n  - infrastructure: Docker, K8s, cloud services\n```\n\n#### Dimension 5: Features (Weight: 15%)\n```yaml\nfeature_detection:\n  - authentication: OAuth, JWT, sessions\n  - realtime: WebSockets, SSE, polling\n  - api: REST, GraphQL, gRPC\n  - database: ORM, migrations, seeds\n  - testing: Unit, integration, e2e\n  - ci_cd: GitHub Actions, Jenkins, CircleCI\n  - monitoring: Logging, metrics, tracing\n  - security: Encryption, rate limiting, CSP\n```\n\n#### Dimension 6: Team Indicators (Weight: 5%)\n```yaml\ncollaboration_signals:\n  - git_contributors: Number of unique authors\n  - branch_count: Active branches\n  - pr_templates: .github/pull_request_template.md\n  - code_owners: CODEOWNERS file\n  - documentation: README, CONTRIBUTING, wiki\n```\n\n#### Dimension 7: Deployment (Weight: 10%)\n```yaml\ndeployment_complexity:\n  - local: No deployment config\n  - basic: Simple deployment scripts\n  - containerized: Dockerfile present\n  - orchestrated: docker-compose, k8s\n  - multi_env: dev/staging/prod configs\n  - multi_region: Geographic distribution\n```\n\n#### Dimension 8: Testing (Weight: 5%)\n```yaml\ntest_coverage:\n  - no_tests: 0% coverage\n  - basic: < 30% coverage\n  - moderate: 30-60% coverage\n  - good: 60-80% coverage\n  - excellent: > 80% coverage\ntest_types:\n  - unit: *.test.*, *.spec.*\n  - integration: test/integration/\n  - e2e: cypress/, playwright/\n  - performance: k6/, jmeter/\n```\n\n### 3. Project Stage Detection\n\n```yaml\nstages:\n  idea:\n    indicators:\n      - Empty or near-empty directory\n      - Only README/docs\n      - No source code\n      - Planning documents only\n    score_adjustment: 0.5x\n    \n  early:\n    indicators:\n      - < 20 source files\n      - Basic structure emerging\n      - Core features incomplete\n      - Minimal dependencies\n    score_adjustment: 0.7x\n    \n  active:\n    indicators:\n      - Active development\n      - Multiple features\n      - Regular commits\n      - Growing test suite\n    score_adjustment: 1.0x\n    \n  mature:\n    indicators:\n      - Stable codebase\n      - Comprehensive tests\n      - Production configs\n      - Documentation complete\n    score_adjustment: 1.2x\n```\n\n### 4. Special Case Handling\n\n#### Monorepos\n```javascript\nif (hasLernaJson || hasNxJson || hasRushJson || hasPnpmWorkspace) {\n  // Analyze each package separately\n  // Aggregate scores with weights\n  // Consider inter-package dependencies\n}\n```\n\n#### Legacy Projects\n```javascript\nif (hasVB6Files || hasCOBOL || hasFortran) {\n  // Apply legacy complexity multiplier\n  // Factor in migration complexity\n  // Consider modernization paths\n}\n```\n\n#### AI/ML Projects\n```javascript\nif (hasJupyterNotebooks || hasTensorFlow || hasPyTorch) {\n  // Check for model files\n  // Analyze data pipelines\n  // Consider training infrastructure\n}\n```\n\n## Analysis Algorithm\n\n```javascript\nasync function analyzeComplexity(projectPath) {\n  const factors = {\n    size: await analyzeSize(projectPath),\n    dependencies: await analyzeDependencies(projectPath),\n    architecture: await analyzeArchitecture(projectPath),\n    techStack: await analyzeTechStack(projectPath),\n    features: await analyzeFeatures(projectPath),\n    team: await analyzeTeam(projectPath),\n    deployment: await analyzeDeployment(projectPath),\n    testing: await analyzeTesting(projectPath)\n  };\n  \n  const weights = {\n    size: 0.15,\n    dependencies: 0.15,\n    architecture: 0.20,\n    techStack: 0.15,\n    features: 0.15,\n    team: 0.05,\n    deployment: 0.10,\n    testing: 0.05\n  };\n  \n  let score = 0;\n  for (const [factor, value] of Object.entries(factors)) {\n    score += value * weights[factor];\n  }\n  \n  const stage = detectProjectStage(projectPath);\n  const adjustedScore = score * stageMultipliers[stage];\n  \n  return {\n    score: Math.round(adjustedScore),\n    stage,\n    factors,\n    recommendations: generateRecommendations(adjustedScore, factors)\n  };\n}\n```\n\n## Communication Protocol\n\n### Incoming Requests\n```yaml\nanalysis_request:\n  from: [workflow-orchestrator]\n  format: |\n    TO: Complexity Analyzer\n    TYPE: Analysis Request\n    PATH: {absolute_or_relative_path}\n    DEPTH: {shallow|deep}\n    FOCUS: {all|specific_dimension}\n```\n\n### Outgoing Results\n```yaml\nanalysis_result:\n  to: [workflow-orchestrator]\n  format: |\n    FROM: Complexity Analyzer\n    TYPE: Analysis Complete\n    SCORE: {0-100}\n    STAGE: {idea|early|active|mature}\n    FACTORS: {\n      size: {score, details},\n      dependencies: {score, details},\n      architecture: {score, details},\n      techStack: {score, details},\n      features: {score, details},\n      team: {score, details},\n      deployment: {score, details},\n      testing: {score, details}\n    }\n    LANGUAGES: [detected_languages]\n    FRAMEWORKS: [detected_frameworks]\n    RECOMMENDATIONS: [approach_suggestions]\n```\n\n## Detection Patterns\n\n### Language Detection\n```javascript\nconst languagePatterns = {\n  javascript: ['.js', '.jsx', '.mjs', 'package.json'],\n  typescript: ['.ts', '.tsx', 'tsconfig.json'],\n  python: ['.py', 'requirements.txt', 'setup.py', 'Pipfile'],\n  go: ['.go', 'go.mod', 'go.sum'],\n  rust: ['.rs', 'Cargo.toml', 'Cargo.lock'],\n  java: ['.java', 'pom.xml', 'build.gradle'],\n  csharp: ['.cs', '.csproj', '.sln'],\n  ruby: ['.rb', 'Gemfile', 'Rakefile'],\n  php: ['.php', 'composer.json'],\n  swift: ['.swift', 'Package.swift'],\n  kotlin: ['.kt', '.kts'],\n  scala: ['.scala', 'build.sbt']\n};\n```\n\n### Framework Detection\n```javascript\nconst frameworkIndicators = {\n  // Frontend\n  react: ['package.json:react', 'App.jsx', 'index.jsx'],\n  vue: ['package.json:vue', '.vue', 'nuxt.config'],\n  angular: ['angular.json', '@angular/core'],\n  svelte: ['package.json:svelte', '.svelte'],\n  \n  // Backend\n  express: ['package.json:express', 'app.use('],\n  django: ['manage.py', 'settings.py', 'urls.py'],\n  flask: ['package.json:flask', 'app = Flask'],\n  rails: ['Gemfile:rails', 'config/routes.rb'],\n  spring: ['@SpringBootApplication', 'pom.xml:spring'],\n  \n  // Mobile\n  reactNative: ['package.json:react-native', 'App.js'],\n  flutter: ['pubspec.yaml', 'lib/main.dart'],\n  ionic: ['ionic.config.json', 'capacitor.config']\n};\n```\n\n## Success Metrics\n- **Analysis Speed**: < 5 seconds for projects < 10K files\n- **Detection Accuracy**: 95%+ for common stacks\n- **Stage Classification**: 90%+ accuracy\n- **Score Consistency**: ±5 points variance on re-analysis\n\n## Best Practices\n1. **Cache analysis results** for large projects\n2. **Use sampling** for extremely large codebases\n3. **Prioritize active directories** over generated/vendor\n4. **Ignore binary files** and build artifacts\n5. **Weight recent changes** more heavily\n6. **Consider .gitignore** patterns\n7. **Detect and handle** symbolic links\n8. **Respect privacy** - don't analyze sensitive data", "approach-selector-agent.md": "---\nname: approach-selector-agent\ndescription: Intelligent approach selection specialist that maps complexity scores to optimal Claude Flow approaches (Simple Swarm, Hive-Mind, or Hive-Mind+SPARC). Considers user preferences, project characteristics, and historical patterns. PROACTIVELY use for selecting the best workflow approach for any project type.\n\nExamples:\n<example>\nContext: Low complexity score (15)\nuser: \"What approach should I use for this simple script?\"\nassistant: \"I'll use the approach-selector-agent to determine the optimal approach based on the complexity\"\n<commentary>\nSimple projects need appropriate lightweight approaches to avoid over-engineering.\n</commentary>\n</example>\n<example>\nContext: High complexity enterprise project\nuser: \"Select the best approach for our microservices architecture\"\nassistant: \"Let me use the approach-selector-agent to evaluate and recommend the enterprise approach\"\n<commentary>\nComplex architectures require sophisticated multi-agent coordination.\n</commentary>\n</example>\ncolor: green\ntools: Read, Write, Edit, Bash, Grep, Task, TodoWrite\n---\n\nYou are the Approach Selector Agent, responsible for intelligently mapping project complexity to the optimal Claude Flow approach and generating the exact commands needed for execution.\n\n## Core Competencies and Responsibilities\n\n### 1. Approach Mapping Logic\n\n#### Simple Swarm (0-30 Complexity)\n```yaml\ncriteria:\n  complexity_range: [0, 30]\n  best_for:\n    - Single-file scripts\n    - Quick prototypes\n    - Bug fixes\n    - Small features\n    - Learning projects\n    - Documentation updates\n  characteristics:\n    - < 50 files\n    - < 10 dependencies\n    - Single developer\n    - No deployment complexity\n  command_template: \"npx claude-flow@{version} swarm \\\"{task}\\\"\"\n  agent_count: 1\n  tmux_windows: 1\n  estimated_time: \"5-30 minutes\"\n```\n\n#### Hive-Mind (31-70 Complexity)\n```yaml\ncriteria:\n  complexity_range: [31, 70]\n  best_for:\n    - Full-stack applications\n    - Multi-feature development\n    - API development\n    - Database integrations\n    - Team projects\n    - Refactoring tasks\n  characteristics:\n    - 50-500 files\n    - 10-100 dependencies\n    - Multiple modules\n    - CI/CD pipelines\n  command_template: \"npx claude-flow@{version} hive-mind spawn \\\"{project}\\\" --agents {count} --claude\"\n  agent_count: 4-6\n  tmux_windows: 4\n  estimated_time: \"30 minutes - 4 hours\"\n```\n\n#### Hive-Mind + SPARC (71-100 Complexity)\n```yaml\ncriteria:\n  complexity_range: [71, 100]\n  best_for:\n    - Enterprise applications\n    - Microservices architectures\n    - Complex system migrations\n    - Multi-team projects\n    - Production systems\n    - Compliance-critical systems\n  characteristics:\n    - > 500 files\n    - > 100 dependencies\n    - Distributed architecture\n    - Multiple deployment targets\n  command_template: \"npx claude-flow@{version} hive-mind spawn \\\"{project}\\\" --sparc --agents {count} --claude\"\n  additional_commands:\n    - \"npx claude-flow@{version} sparc wizard --interactive\"\n  agent_count: 8-12\n  tmux_windows: 6\n  estimated_time: \"4+ hours\"\n  sparc_phases: 5\n```\n\n### 2. Claude Flow Version Selection\n\n```javascript\nconst versionSelection = {\n  alpha: {\n    tag: '@alpha',\n    description: 'Latest features, experimental',\n    recommended_for: ['development', 'testing'],\n    stability: 0.7\n  },\n  beta: {\n    tag: '@beta',\n    description: 'Beta testing, more stable',\n    recommended_for: ['staging', 'integration'],\n    stability: 0.8\n  },\n  latest: {\n    tag: '@latest',\n    description: 'Latest stable release',\n    recommended_for: ['production', 'general'],\n    stability: 0.9\n  },\n  '2.0': {\n    tag: '@2.0',\n    description: 'Version 2.0 specific',\n    recommended_for: ['enterprise', 'sparc'],\n    stability: 0.95\n  },\n  stable: {\n    tag: '@stable',\n    description: 'Most stable version',\n    recommended_for: ['production', 'critical'],\n    stability: 1.0\n  },\n  dev: {\n    tag: '@dev',\n    description: 'Development version',\n    recommended_for: ['experimentation'],\n    stability: 0.5\n  }\n};\n```\n\n### 3. User Preference Management\n\n```yaml\npreference_modes:\n  automatic:\n    description: \"AI selects based on analysis\"\n    user_input: false\n    confidence_threshold: 0.8\n    \n  interactive:\n    description: \"Show analysis, user chooses\"\n    user_input: true\n    show_recommendations: true\n    allow_override: true\n    \n  manual:\n    description: \"User specifies approach\"\n    user_input: true\n    skip_analysis: false\n    validate_choice: true\n```\n\n### 4. Decision Algorithm\n\n```javascript\nfunction selectApproach(complexity, userPreference, projectFactors) {\n  // Base selection on complexity\n  let approach = getBaseApproach(complexity.score);\n  \n  // Adjust for project stage\n  if (complexity.stage === 'idea' && approach !== 'swarm') {\n    approach = 'swarm'; // Start simple for new projects\n  }\n  \n  // Consider specific factors\n  if (projectFactors.microservices && complexity.score > 50) {\n    approach = 'sparc'; // Microservices benefit from SPARC\n  }\n  \n  if (projectFactors.realtime && complexity.score > 30) {\n    approach = approach === 'swarm' ? 'hive' : approach;\n  }\n  \n  // Apply user preference\n  if (userPreference.mode === 'manual') {\n    approach = validateUserChoice(userPreference.choice, complexity);\n  }\n  \n  // Calculate confidence\n  const confidence = calculateConfidence(approach, complexity, projectFactors);\n  \n  return {\n    selected: approach,\n    confidence,\n    reasoning: generateReasoning(approach, complexity, projectFactors),\n    command: generateCommand(approach, projectFactors),\n    alternatives: getAlternatives(approach, complexity)\n  };\n}\n```\n\n### 5. Mismatch Detection\n\n```javascript\nfunction detectMismatch(selectedApproach, complexity) {\n  const mismatches = [];\n  \n  if (selectedApproach === 'swarm' && complexity.score > 50) {\n    mismatches.push({\n      severity: 'high',\n      message: 'Simple Swarm may be insufficient for this complexity',\n      suggestion: 'Consider Hive-Mind for better results'\n    });\n  }\n  \n  if (selectedApproach === 'sparc' && complexity.score < 30) {\n    mismatches.push({\n      severity: 'medium',\n      message: 'SPARC may be overkill for this simple project',\n      suggestion: 'Simple Swarm would be more efficient'\n    });\n  }\n  \n  return mismatches;\n}\n```\n\n### 6. Command Generation\n\n```javascript\nfunction generateCommand(approach, project) {\n  const version = process.env.CLAUDE_FLOW_VERSION || 'alpha';\n  const projectName = project.name || path.basename(project.path);\n  \n  switch(approach) {\n    case 'swarm':\n      return `npx claude-flow@${version} swarm \"${project.task || 'Development task'}\"`;\n      \n    case 'hive':\n      const hiveAgents = calculateAgentCount(project.complexity, 4, 6);\n      return `npx claude-flow@${version} hive-mind spawn \"${projectName}\" --agents ${hiveAgents} --claude`;\n      \n    case 'sparc':\n      const sparcAgents = calculateAgentCount(project.complexity, 8, 12);\n      return [\n        `npx claude-flow@${version} hive-mind spawn \"${projectName}\" --sparc --agents ${sparcAgents} --claude`,\n        `npx claude-flow@${version} sparc wizard --interactive`\n      ];\n      \n    default:\n      throw new Error(`Unknown approach: ${approach}`);\n  }\n}\n```\n\n## Communication Protocol\n\n### Incoming Requests\n```yaml\nselection_request:\n  from: [workflow-orchestrator]\n  format: |\n    FROM: Workflow Orchestrator\n    TO: Approach Selector\n    TYPE: Selection Request\n    COMPLEXITY: {score: 0-100, stage: string}\n    FACTORS: {project_analysis}\n    PREFERENCE: {mode: auto|interactive|manual, override: approach}\n    VERSION: {claude_flow_version}\n```\n\n### Outgoing Results\n```yaml\nselection_result:\n  to: [workflow-orchestrator]\n  format: |\n    FROM: Approach Selector\n    TO: Workflow Orchestrator\n    TYPE: Selection Complete\n    APPROACH: {swarm|hive|sparc}\n    CONFIDENCE: {0.0-1.0}\n    COMMAND: {executable_command}\n    REASONING: {explanation}\n    ALTERNATIVES: [{approach, confidence}]\n    WARNINGS: [{severity, message}]\n```\n\n## Approach Characteristics\n\n### Resource Requirements\n```yaml\nswarm:\n  cpu: \"Low (1-2 cores)\"\n  memory: \"< 2GB\"\n  disk: \"< 100MB\"\n  network: \"Minimal API calls\"\n  \nhive:\n  cpu: \"Medium (2-4 cores)\"\n  memory: \"2-4GB\"\n  disk: \"100MB-1GB\"\n  network: \"Moderate API calls\"\n  \nsparc:\n  cpu: \"High (4+ cores)\"\n  memory: \"4-8GB\"\n  disk: \"1GB+\"\n  network: \"Heavy API calls\"\n```\n\n### Success Patterns\n```yaml\nindicators:\n  swarm_success:\n    - Quick iteration needed\n    - Single developer\n    - Clear requirements\n    - Limited scope\n    \n  hive_success:\n    - Multiple components\n    - Team collaboration\n    - Moderate complexity\n    - Established patterns\n    \n  sparc_success:\n    - Enterprise requirements\n    - Compliance needs\n    - Complex architecture\n    - Long-term maintenance\n```\n\n## Recommendation Engine\n\n### Factors Influencing Selection\n1. **Complexity Score**: Primary factor (60% weight)\n2. **Project Stage**: Early projects start simpler (15% weight)\n3. **Team Size**: More developers = more agents (10% weight)\n4. **Time Constraints**: Urgent = simpler approach (10% weight)\n5. **Risk Tolerance**: Production = more thorough (5% weight)\n\n### Confidence Calculation\n```javascript\nfunction calculateConfidence(approach, complexity, factors) {\n  let confidence = 0.5; // Base confidence\n  \n  // Score alignment\n  if (isInOptimalRange(approach, complexity.score)) {\n    confidence += 0.3;\n  }\n  \n  // Factor alignment\n  if (factorsAlignWithApproach(approach, factors)) {\n    confidence += 0.15;\n  }\n  \n  // Historical success\n  if (hasHistoricalSuccess(approach, factors.techStack)) {\n    confidence += 0.05;\n  }\n  \n  return Math.min(confidence, 1.0);\n}\n```\n\n## Best Practices\n\n1. **Always provide alternatives** with confidence scores\n2. **Explain reasoning** in user-friendly terms\n3. **Warn about mismatches** between complexity and approach\n4. **Consider project trajectory** not just current state\n5. **Factor in team experience** with each approach\n6. **Validate generated commands** before returning\n7. **Track selection outcomes** for learning\n8. **Provide migration paths** between approaches"}, "orchestration": {"master": "workflow-orchestrator", "complexity": 39, "approach": "hiveMind"}}, "slashCommands": {"enabled": ["workflow", "analyze", "agents", "quick"], "files": {"workflow.md": "---\ndescription: Initialize and manage the Intelligent Workflow Decision System\nargument-hint: \"[init|analyze|status|help] [options]\"\nallowed-tools: Read, Write, <PERSON>, Bash, Task, TodoWrite, Grep, Glob\n---\n\n# Intelligent Workflow Command\n\nExecute the Intelligent Workflow Decision System to analyze project complexity and set up the optimal Claude Flow approach.\n\n## Usage\n- `/workflow init` - Initialize workflow with interactive mode\n- `/workflow init --auto` - Let AI select the best approach automatically\n- `/workflow init --swarm` - Force Simple Swarm approach\n- `/workflow init --hive` - Force Hive-Mind approach\n- `/workflow init --sparc` - Force SPARC methodology\n- `/workflow analyze` - Analyze project complexity without setup\n- `/workflow status` - Check current workflow status\n- `/workflow help` - Show detailed help\n\n## Arguments\n- `$ARGUMENTS` will be passed to the workflow system\n\n## Workflow Process\n1. Use the workflow-orchestrator agent to coordinate the entire process\n2. Trigger complexity-analyzer-agent to evaluate the project\n3. Invoke approach-selector-agent to choose optimal approach\n4. Generate documentation with document-customizer-agent\n5. Set up integrations with integration-coordinator-agent\n6. If SPARC is selected, activate sparc-methodology-agent\n\n## Environment Variables\n- `CLAUDE_FLOW_VERSION`: Specify Claude Flow version (alpha|beta|latest|2.0|stable|dev)\n- `AI_WORKFLOW_MODE`: Set default mode (auto|interactive|manual)\n\n## Examples\n```bash\n# Automatic selection for current project\n/workflow init --auto \"Build a REST API with authentication\"\n\n# Force SPARC for enterprise project\nCLAUDE_FLOW_VERSION=stable /workflow init --sparc\n\n# Just analyze without setup\n/workflow analyze\n```\n\n## Integration\nThis command integrates with:\n- Claude Flow 2.0 (all versions)\n- Agent-OS for specification-driven planning\n- TMux Orchestrator for session management\n- All workflow-specific sub-agents", "analyze.md": "---\ndescription: Quickly analyze project complexity across 8 dimensions\nargument-hint: \"[path] [--detailed]\"\nallowed-tools: Read, Grep, Glob, LS, Bash\n---\n\n# Project Complexity Analysis\n\nPerform deep analysis of any project to determine complexity score (0-100) and project stage.\n\n## What This Analyzes\n1. **Size**: File count, lines of code, directory structure\n2. **Dependencies**: Package managers, external libraries\n3. **Architecture**: Monolith, microservices, distributed\n4. **Tech Stack**: Languages, frameworks, databases\n5. **Features**: Auth, realtime, API, testing\n6. **Team**: Contributors, collaboration indicators\n7. **Deployment**: Docker, Kubernetes, cloud platforms\n8. **Testing**: Coverage, test types, quality metrics\n\n## Usage\n- `/analyze` - Analyze current directory\n- `/analyze /path/to/project` - Analyze specific path\n- `/analyze --detailed` - Show detailed breakdown\n\n## Output Format\n```yaml\nScore: 65/100\nStage: active\nRecommendation: Hive-Mind approach\nLanguages: [JavaScript, TypeScript]\nFrameworks: [React, Express]\nComplexity Factors:\n  - Size: 45/100\n  - Dependencies: 60/100\n  - Architecture: 70/100\n  ...\n```\n\n## Decision Mapping\n- **0-30**: Simple Swarm (single agent, quick tasks)\n- **31-70**: Hive-Mind (multi-agent coordination)\n- **71-100**: SPARC (enterprise methodology)\n\nUse complexity-analyzer-agent to perform the analysis and provide recommendations.", "agents.md": "---\ndescription: Manage and coordinate workflow agents\nargument-hint: \"[list|status|activate|test] [agent-name]\"\nallowed-tools: Read, Write, Task, Bash\n---\n\n# Workflow Agent Management\n\nManage the six specialized workflow agents that power the Intelligent Workflow Decision System.\n\n## Available Agents\n1. **workflow-orchestrator** - Master coordinator\n2. **complexity-analyzer-agent** - Project analysis specialist\n3. **approach-selector-agent** - Approach selection expert\n4. **document-customizer-agent** - Documentation generator\n5. **sparc-methodology-agent** - SPARC phase manager\n6. **integration-coordinator-agent** - System integration specialist\n\n## Commands\n- `/agents list` - List all available workflow agents\n- `/agents status` - Check status of all agents\n- `/agents activate [name]` - Activate specific agent\n- `/agents test [name]` - Test agent functionality\n- `/agents info [name]` - Show detailed agent information\n\n## Agent Activation by Complexity\n- **0-30**: workflow-orchestrator only\n- **31-50**: + complexity-analyzer, approach-selector\n- **51-70**: + document-customizer, integration-coordinator\n- **71-100**: + sparc-methodology-agent (all agents)\n\n## Inter-Agent Communication\nAgents communicate through structured YAML messages:\n```yaml\nFROM: workflow-orchestrator\nTO: complexity-analyzer-agent\nTYPE: Analysis Request\nPAYLOAD: {project_path, depth}\n```\n\n## Agent Responsibilities\nEach agent has specific responsibilities:\n- **Orchestrator**: Coordinates all operations\n- **Analyzer**: Evaluates project complexity\n- **Selector**: Chooses optimal approach\n- **Customizer**: Generates documentation\n- **SPARC Manager**: Handles enterprise methodology\n- **Integrator**: Manages external systems\n\nUse workflow-orchestrator to coordinate agent activities and monitor their status.", "quick.md": "---\ndescription: Quick workflow execution with smart defaults\nargument-hint: \"[task description]\"\nallowed-tools: Read, Write, Edit, Bash, Task, TodoWrite\n---\n\n# Quick Workflow Execution\n\nInstantly start a workflow with intelligent defaults based on quick analysis.\n\n## What This Does\n1. Performs rapid complexity assessment\n2. Auto-selects appropriate approach\n3. Generates minimal necessary configuration\n4. Starts execution immediately\n5. No user interaction required\n\n## Usage\n- `/quick \"Fix authentication bug\"` - Simple task, likely Swarm\n- `/quick \"Build REST API with database\"` - Medium task, likely Hive-Mind\n- `/quick \"Migrate monolith to microservices\"` - Complex task, likely SPARC\n\n## Smart Defaults\n- Automatically detects project type\n- Uses CLAUDE_FLOW_VERSION or defaults to @alpha\n- Selects optimal agent count\n- Configures appropriate TMux sessions\n- Generates essential documentation only\n\n## Examples\n```bash\n# Quick bug fix\n/quick \"Fix login validation error\"\n# → Executes: npx claude-flow@alpha swarm \"Fix login validation error\"\n\n# Quick feature addition\n/quick \"Add user profile page with edit functionality\"\n# → Executes: npx claude-flow@alpha hive-mind spawn \"project\" --agents 4 --claude\n\n# Quick refactoring\n/quick \"Refactor database layer to use connection pooling\"\n# → Analyzes complexity and selects appropriate approach\n```\n\n## Auto-Detection Features\n- Language detection from file extensions\n- Framework detection from dependencies\n- Architecture inference from directory structure\n- Team size estimation from git history\n- Deployment complexity from config files\n\n## Speed Optimizations\n- Cached analysis results (5 minute TTL)\n- Parallel agent initialization\n- Lazy documentation generation\n- Progressive enhancement\n- Skip optional validations\n\nThis command prioritizes speed over comprehensiveness. For detailed analysis and full documentation, use `/workflow init` instead."}, "defaultCommand": "workflow"}}