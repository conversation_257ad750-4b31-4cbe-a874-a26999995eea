---
name: Phase tracking issue
about: Track a delivery phase from GPT5-PLAN.MD
title: "[Phase] <number> — <title>"
labels: ["phase", "priority:high"]
assignees: []
---

## Scope

Link to plan section and describe the scope for this phase.

References:
- GPT5-PLAN.MD section: <link/lines>

## Inputs / Outputs

- Inputs: 
- Outputs: 

## API / Data Models (if applicable)

Summarize any schemas or endpoints.

## Tests

- Unit: 
- Integration: 
- E2E/Perf: 

## Rollback Steps

Describe how to roll back changes safely.

## Success Criteria

- [ ] All deliverables implemented
- [ ] CI green on matrix
- [ ] Docs updated
- [ ] End-of-phase summary posted

---
name: Phase Task
about: Implementation issue for a plan phase
title: "Phase X — <Title>"
labels: ["phase", "priority:high"]
assignees: []
---

## Scope
Describe what this phase delivers.

## Inputs
- Files, configs, prerequisites

## Outputs
- Executables, APIs, docs, schemas

## Technical Specs
- APIs, data models, schemas, file structure

## Testing
- Unit, integration, e2e, performance, security

## Rollback
- Steps to revert all changes safely

## Acceptance Criteria
- Checklist that must pass

## References
- GPT5-PLAN.MD section
- GPT5-REPORT.MD


