name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      debug_enabled:
        type: boolean
        description: 'Run with debug logging'
        required: false
        default: false

jobs:
  test-matrix:
    name: Node tests (matrix)
    runs-on: ${{ matrix.os }}
    timeout-minutes: 20
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        node: [18, 20]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node ${{ matrix.node }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}

      - name: Install dependencies
        run: |
          if [ -f package-lock.json ]; then npm ci; else npm i; fi

      - name: Lint (best-effort)
        continue-on-error: true
        run: |
          if npm run | grep -q "lint"; then npm run lint; else echo "No lint script configured"; fi

      - name: Run tests
        run: npm test

      - name: Generate coverage (if nyc present)
        run: |
          if npm ls nyc >/dev/null 2>&1; then npm run coverage || true; else echo "nyc not installed"; fi

      - name: Upload coverage
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: coverage-${{ matrix.os }}-node${{ matrix.node }}
          path: |
            coverage/**
            test/results/**
          retention-days: 7

  policy:
    name: Policy checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Block YOLO in CI
        run: |
          if grep -R "--dangerously-skip-permissions" -n .; then
            echo "YOLO flags must not be committed in CI paths." >&2
            exit 1
          fi
          echo "YOLO flags not found in committed paths."

      - name: OpenAPI validate (if present)
        run: |
          if [ -f openapi.yaml ]; then
            npx --yes @redocly/cli@latest lint openapi.yaml || true
          fi

  pr-comment:
    name: Comment PR on success
    if: github.event_name == 'pull_request'
    needs: [test-matrix, policy]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '✅ CI checks (tests + policy) passed across matrix.'
            })


