name: PR Validation

on:
  pull_request:
    types: [opened, edited, synchronize, reopened]

jobs:
  validate-pr:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      issues: write
      contents: read
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Validate PR title
        uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            feat
            fix
            docs
            style
            refactor
            perf
            test
            build
            ci
            chore
            revert
          requireScope: false
          subjectPattern: ^[A-Z].*$
          subjectPatternError: |
            The subject "{subject}" found in the pull request title "{title}"
            doesn't match the configured pattern. Please ensure that the subject
            starts with an uppercase letter.

      - name: Check PR size
        uses: actions/github-script@v7
        with:
          script: |
            const pr = context.payload.pull_request;
            const { additions, deletions } = pr;
            const totalChanges = additions + deletions;
            
            let label = '';
            let comment = '';
            
            if (totalChanges < 10) {
              label = 'size/XS';
              comment = 'This is an extra small PR (< 10 lines)';
            } else if (totalChanges < 50) {
              label = 'size/S';
              comment = 'This is a small PR (< 50 lines)';
            } else if (totalChanges < 200) {
              label = 'size/M';
              comment = 'This is a medium PR (< 200 lines)';
            } else if (totalChanges < 500) {
              label = 'size/L';
              comment = 'This is a large PR (< 500 lines). Consider breaking it down.';
            } else {
              label = 'size/XL';
              comment = '⚠️ This is an extra large PR (>= 500 lines). Please consider splitting it into smaller PRs for easier review.';
            }
            
            // Add label
            await github.rest.issues.addLabels({
              issue_number: pr.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: [label]
            });
            
            // Add comment for large PRs
            if (totalChanges >= 200) {
              await github.rest.issues.createComment({
                issue_number: pr.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            }

      - name: Check for merge conflicts
        uses: actions/github-script@v7
        with:
          script: |
            const pr = context.payload.pull_request;
            
            if (pr.mergeable_state === 'dirty') {
              await github.rest.issues.createComment({
                issue_number: pr.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: '⚠️ This PR has merge conflicts. Please resolve them before merging.'
              });
              
              await github.rest.issues.addLabels({
                issue_number: pr.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                labels: ['merge-conflict']
              });
            }

      - name: Auto-assign reviewers
        uses: actions/github-script@v7
        with:
          script: |
            const pr = context.payload.pull_request;
            
            // Skip if already has reviewers
            if (pr.requested_reviewers && pr.requested_reviewers.length > 0) {
              return;
            }
            
            // Define reviewer assignment rules
            const reviewerRules = {
              'intelligence-engine/': ['@maintainer1'],
              'configs/': ['@maintainer2'],
              'test/': ['@qa-team'],
              '.github/': ['@devops-team']
            };
            
            // Get changed files
            const { data: files } = await github.rest.pulls.listFiles({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: pr.number
            });
            
            const reviewers = new Set();
            
            // Match files to reviewers
            for (const file of files) {
              for (const [path, users] of Object.entries(reviewerRules)) {
                if (file.filename.startsWith(path)) {
                  users.forEach(user => reviewers.add(user));
                }
              }
            }
            
            // Request reviewers if any matched
            if (reviewers.size > 0) {
              try {
                await github.rest.pulls.requestReviewers({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  pull_number: pr.number,
                  reviewers: Array.from(reviewers).map(r => r.replace('@', ''))
                });
              } catch (error) {
                console.log('Could not auto-assign reviewers:', error.message);
              }
            }

  check-documentation:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: read
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check if documentation needed
        uses: actions/github-script@v7
        with:
          script: |
            const pr = context.payload.pull_request;
            
            // Get changed files
            const { data: files } = await github.rest.pulls.listFiles({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: pr.number
            });
            
            // Check if significant code changes require documentation
            const significantChanges = files.some(file => 
              (file.filename.endsWith('.js') || 
               file.filename.endsWith('.sh') ||
               file.filename.endsWith('.json')) &&
              !file.filename.includes('test/') &&
              file.changes > 50
            );
            
            const hasDocChanges = files.some(file => 
              file.filename.endsWith('.md') ||
              file.filename.includes('docs/')
            );
            
            if (significantChanges && !hasDocChanges) {
              await github.rest.issues.createComment({
                issue_number: pr.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: '📝 This PR contains significant code changes. Please consider updating the documentation if needed.'
              });
              
              await github.rest.issues.addLabels({
                issue_number: pr.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                labels: ['needs-documentation']
              });
            }