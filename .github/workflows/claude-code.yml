name: <PERSON> CI

on:
  pull_request:
    types: [opened, synchronize, reopened]
  issue_comment:
    types: [created]

jobs:
  claude-code:
    # Only run on PR comments that contain '/claude'
    if: |
      (github.event_name == 'pull_request') ||
      (github.event_name == 'issue_comment' && 
       github.event.issue.pull_request && 
       contains(github.event.comment.body, '/claude'))
    
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      issues: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Claude Code
        uses: anthropics/claude-code-action@v1
        with:
          github-app-id: ${{ secrets.CLAUDE_GITHUB_APP_ID }}
          github-app-private-key: ${{ secrets.CLAUDE_GITHUB_APP_PRIVATE_KEY }}
          anthropic-api-key: ${{ secrets.ANTHROPIC_API_KEY }}
          
      - name: Run Claude Code Analysis
        id: claude_analysis
        uses: anthropics/claude-code-action@v1
        with:
          github-app-id: ${{ secrets.CLAUDE_GITHUB_APP_ID }}
          github-app-private-key: ${{ secrets.CLAUDE_GITHUB_APP_PRIVATE_KEY }}
          anthropic-api-key: ${{ secrets.ANTHROPIC_API_KEY }}
          mode: 'review'  # Options: 'review', 'fix', 'test'
          focus-areas: |
            - Code quality and best practices
            - Security vulnerabilities
            - Performance optimizations
            - Test coverage
          custom-instructions: |
            Review the code changes in this PR.
            Focus on the intelligent workflow system implementation.
            Check for proper error handling and edge cases.
            Suggest improvements for maintainability.

      - name: Post Claude Code suggestions
        if: always() && steps.claude_analysis.outputs.suggestions
        uses: actions/github-script@v7
        with:
          script: |
            const suggestions = `${{ steps.claude_analysis.outputs.suggestions }}`;
            const comment = `## Claude Code Review\n\n${suggestions}`;
            
            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  claude-fix:
    # Run when someone comments '/claude fix' on a PR
    if: |
      github.event_name == 'issue_comment' &&
      github.event.issue.pull_request &&
      contains(github.event.comment.body, '/claude fix')
    
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      issues: write
    
    steps:
      - name: Checkout PR
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Run Claude Code Fix
        uses: anthropics/claude-code-action@v1
        with:
          github-app-id: ${{ secrets.CLAUDE_GITHUB_APP_ID }}
          github-app-private-key: ${{ secrets.CLAUDE_GITHUB_APP_PRIVATE_KEY }}
          anthropic-api-key: ${{ secrets.ANTHROPIC_API_KEY }}
          mode: 'fix'
          auto-commit: true
          commit-message: 'fix: Apply Claude Code suggestions'
          
      - name: Comment on success
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '\u2705 Claude Code has applied fixes to this PR!'
            })

  claude-test:
    # Run when someone comments '/claude test' on a PR
    if: |
      github.event_name == 'issue_comment' &&
      github.event.issue.pull_request &&
      contains(github.event.comment.body, '/claude test')
    
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      issues: write
    
    steps:
      - name: Checkout PR
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Generate tests with Claude
        uses: anthropics/claude-code-action@v1
        with:
          github-app-id: ${{ secrets.CLAUDE_GITHUB_APP_ID }}
          github-app-private-key: ${{ secrets.CLAUDE_GITHUB_APP_PRIVATE_KEY }}
          anthropic-api-key: ${{ secrets.ANTHROPIC_API_KEY }}
          mode: 'test'
          test-framework: 'jest'  # Adjust based on your testing framework
          auto-commit: true
          commit-message: 'test: Add Claude-generated tests'
          
      - name: Comment on completion
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '\u2705 Claude Code has generated tests for this PR!'
            })