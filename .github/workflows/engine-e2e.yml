name: Engine E2E (manual)

on:
  workflow_dispatch:

jobs:
  e2e:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        working-directory: engine
        run: |
          npm ci

      - name: Build
        working-directory: engine
        run: npm run build

      - name: Start server
        working-directory: engine
        run: node dist/api/server.js &

      - name: Wait for server
        run: |
          for i in {1..30}; do
            if curl -sSf http://localhost:13800/health >/dev/null; then exit 0; fi; sleep 1; done; exit 1

      - name: Run E2E smoke
        working-directory: engine
        run: node test/e2e-smoke.js


