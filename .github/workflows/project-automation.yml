name: Project Automation

on:
  issues:
    types: [opened, closed, reopened, labeled, unlabeled, assigned]
  pull_request:
    types: [opened, closed, merged, ready_for_review, converted_to_draft]
  issue_comment:
    types: [created]
  workflow_dispatch:
    inputs:
      phase:
        description: 'Phase to transition to'
        required: true
        type: choice
        options:
          - 'phase-1-planning'
          - 'phase-2-architecture'
          - 'phase-3-setup'
          - 'phase-4-core-development'
          - 'phase-5-integration'
          - 'phase-6-testing'
          - 'phase-7-optimization'
          - 'phase-8-documentation'
          - 'phase-9-deployment'
          - 'phase-10-monitoring'
          - 'phase-11-maintenance'
          - 'phase-12-review'

permissions:
  issues: write
  pull-requests: write
  repository-projects: write
  contents: read

jobs:
  auto-triage:
    runs-on: ubuntu-latest
    if: github.event_name == 'issues' && github.event.action == 'opened'
    
    steps:
      - name: Auto-label new issues
        uses: actions/github-script@v7
        with:
          script: |
            const issue = context.payload.issue;
            const labels = [];
            
            // Determine phase based on issue title or body
            const content = (issue.title + ' ' + issue.body).toLowerCase();
            
            // Phase detection
            if (content.includes('phase 1') || content.includes('planning')) {
              labels.push('phase-1-planning');
            } else if (content.includes('phase 2') || content.includes('architecture')) {
              labels.push('phase-2-architecture');
            } else if (content.includes('phase 3') || content.includes('setup')) {
              labels.push('phase-3-setup');
            } else if (content.includes('phase 4') || content.includes('core')) {
              labels.push('phase-4-core-development');
            } else if (content.includes('phase 5') || content.includes('integration')) {
              labels.push('phase-5-integration');
            } else if (content.includes('phase 6') || content.includes('testing')) {
              labels.push('phase-6-testing');
            } else if (content.includes('phase 7') || content.includes('optimization')) {
              labels.push('phase-7-optimization');
            } else if (content.includes('phase 8') || content.includes('documentation')) {
              labels.push('phase-8-documentation');
            } else if (content.includes('phase 9') || content.includes('deployment')) {
              labels.push('phase-9-deployment');
            } else if (content.includes('phase 10') || content.includes('monitoring')) {
              labels.push('phase-10-monitoring');
            } else if (content.includes('phase 11') || content.includes('maintenance')) {
              labels.push('phase-11-maintenance');
            } else if (content.includes('phase 12') || content.includes('review')) {
              labels.push('phase-12-review');
            }
            
            // Priority detection
            if (content.includes('urgent') || content.includes('critical')) {
              labels.push('priority-high');
            } else if (content.includes('important')) {
              labels.push('priority-medium');
            } else {
              labels.push('priority-low');
            }
            
            // Type detection
            if (content.includes('bug') || content.includes('error') || content.includes('fix')) {
              labels.push('bug');
            } else if (content.includes('feature') || content.includes('enhancement')) {
              labels.push('enhancement');
            } else if (content.includes('docs') || content.includes('documentation')) {
              labels.push('documentation');
            }
            
            // Add status label
            labels.push('status-todo');
            
            // Apply labels
            if (labels.length > 0) {
              await github.rest.issues.addLabels({
                issue_number: issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                labels: labels
              });
            }

  track-phase-progress:
    runs-on: ubuntu-latest
    if: github.event_name == 'issues' && github.event.action == 'closed'
    
    steps:
      - name: Update phase progress
        uses: actions/github-script@v7
        with:
          script: |
            const issue = context.payload.issue;
            
            // Get all issues to calculate progress
            const allIssues = await github.paginate(github.rest.issues.listForRepo, {
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'all',
              per_page: 100
            });
            
            // Group by phase
            const phases = {};
            for (let i = 1; i <= 12; i++) {
              phases[`phase-${i}`] = { total: 0, closed: 0 };
            }
            
            allIssues.forEach(issue => {
              issue.labels.forEach(label => {
                if (label.name.startsWith('phase-')) {
                  const phaseNum = label.name.match(/phase-(\d+)/)[1];
                  phases[`phase-${phaseNum}`].total++;
                  if (issue.state === 'closed') {
                    phases[`phase-${phaseNum}`].closed++;
                  }
                }
              });
            });
            
            // Create progress report
            let progressReport = '## Build Plan Progress Report\n\n';
            progressReport += '| Phase | Progress | Status |\n';
            progressReport += '|-------|----------|--------|\n';
            
            const phaseNames = [
              'Planning', 'Architecture', 'Setup', 'Core Development',
              'Integration', 'Testing', 'Optimization', 'Documentation',
              'Deployment', 'Monitoring', 'Maintenance', 'Review'
            ];
            
            for (let i = 1; i <= 12; i++) {
              const phase = phases[`phase-${i}`];
              const percentage = phase.total > 0 ? Math.round((phase.closed / phase.total) * 100) : 0;
              const status = percentage === 100 ? '✅ Complete' : 
                            percentage > 50 ? '🔄 In Progress' : 
                            percentage > 0 ? '🟡 Started' : '⏳ Not Started';
              
              progressReport += `| Phase ${i}: ${phaseNames[i-1]} | ${phase.closed}/${phase.total} (${percentage}%) | ${status} |\n`;
            }
            
            // Find or create progress tracking issue
            const progressIssues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: 'progress-tracking',
              state: 'open'
            });
            
            if (progressIssues.data.length > 0) {
              // Update existing progress issue
              await github.rest.issues.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: progressIssues.data[0].number,
                body: progressReport + '\n\n*Last updated: ' + new Date().toISOString() + '*'
              });
            }

  phase-transition:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    
    steps:
      - name: Transition to new phase
        uses: actions/github-script@v7
        with:
          script: |
            const targetPhase = '${{ github.event.inputs.phase }}';
            const phaseNum = parseInt(targetPhase.match(/phase-(\d+)/)[1]);
            
            // Create milestone for the phase if not exists
            const milestones = await github.rest.issues.listMilestones({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open'
            });
            
            let milestone = milestones.data.find(m => m.title === `Phase ${phaseNum}`);
            if (!milestone) {
              const result = await github.rest.issues.createMilestone({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: `Phase ${phaseNum}`,
                description: `Milestone for ${targetPhase}`,
                due_on: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
              });
              milestone = result.data;
            }
            
            // Update all issues in this phase to use the milestone
            const phaseIssues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: targetPhase,
              state: 'open'
            });
            
            for (const issue of phaseIssues.data) {
              await github.rest.issues.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issue.number,
                milestone: milestone.number
              });
            }
            
            // Create phase kickoff issue
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `🚀 Phase ${phaseNum} Kickoff`,
              body: `## Phase ${phaseNum} has begun!\n\n### Objectives\n- [ ] Complete all issues labeled with \`${targetPhase}\`\n- [ ] Review and test all changes\n- [ ] Update documentation\n\n### Team\ncc @Beaulewis1977\n\n### Timeline\nTarget completion: ${new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}`,
              labels: [targetPhase, 'phase-kickoff'],
              milestone: milestone.number
            });

  command-processor:
    runs-on: ubuntu-latest
    if: github.event_name == 'issue_comment' && github.event.issue.pull_request == null
    
    steps:
      - name: Process slash commands
        uses: actions/github-script@v7
        with:
          script: |
            const comment = context.payload.comment.body;
            const issue = context.payload.issue;
            
            // Status update commands
            if (comment.startsWith('/status')) {
              const status = comment.split(' ')[1];
              const validStatuses = ['todo', 'in-progress', 'blocked', 'review', 'done'];
              
              if (validStatuses.includes(status)) {
                // Remove old status labels
                const currentLabels = issue.labels.filter(l => !l.name.startsWith('status-'));
                
                // Add new status label
                await github.rest.issues.setLabels({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issue.number,
                  labels: [...currentLabels.map(l => l.name), `status-${status}`]
                });
                
                // Add reaction to confirm
                await github.rest.reactions.createForIssueComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  comment_id: context.payload.comment.id,
                  content: '+1'
                });
              }
            }
            
            // Phase assignment
            if (comment.startsWith('/phase')) {
              const phaseNum = comment.match(/\/phase (\d+)/)?.[1];
              if (phaseNum && phaseNum >= 1 && phaseNum <= 12) {
                const phaseLabel = `phase-${phaseNum}`;
                
                // Remove other phase labels
                const currentLabels = issue.labels.filter(l => !l.name.startsWith('phase-'));
                
                // Add new phase label
                await github.rest.issues.setLabels({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issue.number,
                  labels: [...currentLabels.map(l => l.name), phaseLabel]
                });
                
                await github.rest.reactions.createForIssueComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  comment_id: context.payload.comment.id,
                  content: 'rocket'
                });
              }
            }
            
            // Priority update
            if (comment.startsWith('/priority')) {
              const priority = comment.split(' ')[1];
              const validPriorities = ['low', 'medium', 'high', 'critical'];
              
              if (validPriorities.includes(priority)) {
                // Remove old priority labels
                const currentLabels = issue.labels.filter(l => !l.name.startsWith('priority-'));
                
                // Add new priority label
                await github.rest.issues.setLabels({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issue.number,
                  labels: [...currentLabels.map(l => l.name), `priority-${priority}`]
                });
                
                await github.rest.reactions.createForIssueComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  comment_id: context.payload.comment.id,
                  content: 'eyes'
                });
              }
            }