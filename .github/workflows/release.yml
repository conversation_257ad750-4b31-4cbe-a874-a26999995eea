name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.0.0)'
        required: true
        type: string

permissions:
  contents: write
  packages: write

jobs:
  release:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          registry-url: 'https://registry.npmjs.org'

      - name: Get version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            VERSION="${{ github.event.inputs.version }}"
          else
            VERSION=${GITHUB_REF#refs/tags/}
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Releasing version: $VERSION"

      - name: Generate changelog
        id: changelog
        run: |
          # Generate changelog from commits since last tag
          LAST_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
          if [ -z "$LAST_TAG" ]; then
            CHANGELOG=$(git log --pretty=format:"- %s (%h)" HEAD)
          else
            CHANGELOG=$(git log --pretty=format:"- %s (%h)" ${LAST_TAG}..HEAD)
          fi
          
          # Save to file for multiline output
          echo "$CHANGELOG" > changelog.txt
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ steps.version.outputs.version }}
          name: Release ${{ steps.version.outputs.version }}
          body: |
            ## What's Changed
            
            ${{ steps.changelog.outputs.changelog }}
            
            ## Installation
            
            ```bash
            # Clone the repository
            git clone https://github.com/${{ github.repository }}.git
            cd master-workflow
            
            # Run installation
            ./install-production.sh
            ```
            
            ## Documentation
            
            - [README](https://github.com/${{ github.repository }}/blob/main/README.md)
            - [Implementation Guide](https://github.com/${{ github.repository }}/blob/main/IMPLEMENTATION-COMPLETE.md)
            - [Migration Guide](https://github.com/${{ github.repository }}/blob/main/MIGRATION-GUIDE.md)
            
            ---
            **Full Changelog**: https://github.com/${{ github.repository }}/compare/${{ steps.version.outputs.last_tag }}...${{ steps.version.outputs.version }}
          draft: false
          prerelease: ${{ contains(steps.version.outputs.version, '-') }}
          generate_release_notes: true

      - name: Create release artifacts
        run: |
          # Create release archive
          mkdir -p dist
          tar -czf dist/master-workflow-${{ steps.version.outputs.version }}.tar.gz \
            --exclude='.git' \
            --exclude='node_modules' \
            --exclude='dist' \
            --exclude='.github' \
            .
          
          # Create checksums
          cd dist
          sha256sum *.tar.gz > checksums.txt
          cd ..

      - name: Upload release artifacts
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ steps.version.outputs.version }}
          files: |
            dist/*.tar.gz
            dist/checksums.txt

  docker-release:
    runs-on: ubuntu-latest
    needs: release
    if: success()
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Get version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            VERSION="${{ github.event.inputs.version }}"
          else
            VERSION=${GITHUB_REF#refs/tags/}
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Create Dockerfile if not exists
        run: |
          if [ ! -f Dockerfile ]; then
            cat > Dockerfile << 'EOF'
FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application files
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

USER nodejs

# Expose port (adjust as needed)
EXPOSE 3000

# Start application
CMD ["node", "workflow-runner.js"]
EOF
          fi

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            ghcr.io/${{ github.repository }}:${{ steps.version.outputs.version }}
            ghcr.io/${{ github.repository }}:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max