version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
    groups:
      minor-and-patch:
        patterns: ["*"]
        update-types: ["minor", "patch"]
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
version: 2
updates:
  # Enable version updates for npm
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "08:00"
    open-pull-requests-limit: 10
    reviewers:
      - "Beaulewis1977"
    labels:
      - "dependencies"
      - "automated"
    commit-message:
      prefix: "chore"
      include: "scope"
    pull-request-branch-name:
      separator: "-"
    allow:
      - dependency-type: "all"
    ignore:
      # Add any dependencies you want to ignore here
      # - dependency-name: "example-package"
      #   versions: ["1.x", "2.x"]

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "08:00"
    open-pull-requests-limit: 5
    reviewers:
      - "Beaulewis1977"
    labels:
      - "github-actions"
      - "automated"
    commit-message:
      prefix: "ci"
      include: "scope"