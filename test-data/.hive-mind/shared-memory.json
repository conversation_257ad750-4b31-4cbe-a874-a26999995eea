{"entries": {"task_selection_test-task-1": {"taskId": "test-task-1", "selectedAgent": "code-analyzer", "prediction": {"successProbability": 0.3068457059562206, "confidence": 0.3076923076923077, "estimatedDuration": 1058.5246235132217, "riskFactors": ["low_success_probability"], "optimizations": [{"type": "user_experience", "priority": "high", "description": "User satisfaction is below target. Review workflow UX and feedback mechanisms.", "impact": "adoption"}, {"type": "resource_optimization", "priority": "medium", "description": "Resource usage could be optimized. Consider caching or batch processing.", "impact": "cost_efficiency"}], "similarPatterns": 0, "neuralOutput": [0.11369141191244125, 0.1080457866191864, 0.10585246235132217, 0.1366393268108368, 0.06638308614492416, 0.23368914425373077, 0.10054631531238556, 0.13515245914459229]}, "timestamp": 1755114805840}, "global_neural_metrics": {"totalLearned": 2, "successRate": 1, "lastUpdate": 1755114808993}, "neural_status": {"initialized": true, "wasmEnabled": false, "patterns": {"total": 1, "memoryUsage": 0.0001}, "metrics": {"totalWorkflows": 1, "successfulWorkflows": 1, "totalTime": 45000, "averageTime": 45000, "errorRate": 0, "userSatisfaction": 0.45, "resourceEfficiency": 0.8333333333333335}, "performance": {"predictionsServed": 3, "averagePredictionTime": 0.3520000000000001, "modelAccuracy": 0, "trainingIterations": 1}, "trends": {"trend": "insufficient_data", "confidence": 0}, "trainingQueue": 0, "modelWeights": 4856, "lastSave": 0}, "task_selection_test-distribute-task": {"taskId": "test-distribute-task", "selectedAgent": "code-analyzer", "prediction": {"successProbability": 0.37896822438021543, "confidence": 0.2815094482873225, "estimatedDuration": 1092.9949581623077, "riskFactors": ["low_success_probability"], "optimizations": [{"type": "user_experience", "priority": "high", "description": "User satisfaction is below target. Review workflow UX and feedback mechanisms.", "impact": "adoption"}, {"type": "pattern_optimization", "priority": "medium", "description": "Found 1 similar successful patterns. Consider adopting their characteristics.", "patterns": [null], "impact": "success_rate"}], "similarPatterns": 1, "neuralOutput": [0.11362896859645844, 0.11122355610132217, 0.10929949581623077, 0.131315678358078, 0.06660816073417664, 0.23317785561084747, 0.09501491487026215, 0.13973136246204376]}, "timestamp": 1755114815107}, "learned_pattern_test": {"id": "test-pattern", "workflowData": {"type": "test"}, "outcomeData": {"success": true}, "timestamp": 1755114818190}, "learned_pattern_test-task-outcome": {"workflowData": {"id": "test-task-outcome", "type": "analysis", "workflowType": "analysis", "projectType": "web", "taskCount": 1, "duration": 45000, "complexity": 6, "userInteractions": 2, "errorCount": 0, "resourceUsage": 0.4, "projectSize": 12000, "primaryLanguage": "javascript", "agentType": "code-analyzer", "estimatedTokens": 25000, "contextUsage": 0.125}, "outcomeData": {"success": true, "duration": 45000, "quality": 0.9, "userRating": 4.5, "errors": [], "resourceUsage": {"cpu": 0.4, "memory": 0.3}, "optimizationPotential": 0.7}, "pattern": {"id": "analysis_web_0_javascript", "features": {"0": 0.009999999776482582, "1": 0.44999998807907104, "2": 0.6000000238418579, "3": 0.019997334107756615, "4": 0, "5": 0.4000000059604645, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 1, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "outcomes": [{"success": true, "duration": 45000, "quality": 0.9, "userRating": 4.5, "errors": [], "resourceUsage": {"cpu": 0.4, "memory": 0.3}, "optimizationPotential": 0.7}], "successRate": 1, "lastUsed": 1755114808943, "usageCount": 1}, "timestamp": 1755114808951}}, "metadata": {"task_selection_test-task-1": {"namespace": "shared_state", "dataType": "persistent", "version": 1, "createdAt": 1755114773448, "updatedAt": 1755114805840, "expiresAt": null, "accessCount": 0, "size": 792, "compressed": false}, "global_neural_metrics": {"namespace": "cross_agent", "dataType": "persistent", "version": 1, "createdAt": 1755114776543, "updatedAt": 1755114808993, "expiresAt": null, "accessCount": 1, "size": 61, "compressed": false}, "neural_status": {"namespace": "cross_agent", "dataType": "persistent", "version": 1, "createdAt": 1755114770294, "updatedAt": 1755114817184, "expiresAt": null, "accessCount": 1, "size": 482, "compressed": false, "lastAccessed": 1755114818173}, "task_selection_test-distribute-task": {"namespace": "shared_state", "dataType": "persistent", "version": 1, "createdAt": 1755114782629, "updatedAt": 1755114815107, "expiresAt": null, "accessCount": 0, "size": 821, "compressed": false}, "learned_pattern_test": {"namespace": "cross_agent", "dataType": "persistent", "version": 1, "createdAt": 1755114785699, "updatedAt": 1755114818190, "expiresAt": 1755114878190, "accessCount": 1, "size": 109, "compressed": false, "lastAccessed": 1755114818219}, "learned_pattern_test-task-outcome": {"namespace": "cross_agent", "dataType": "persistent", "version": 1, "createdAt": 1755114776512, "updatedAt": 1755114808951, "expiresAt": 1755201208951, "accessCount": 0, "size": 1096, "compressed": false}}, "versions": {"neural_status": 1, "learned_pattern_test": 1}, "timestamp": 1755114818248, "stats": {"reads": 2, "writes": 2, "hits": 2, "misses": 0, "evictions": 0, "gcRuns": 0, "totalMemoryUsed": 3938, "averageReadTime": 16.5, "averageWriteTime": 27.5, "concurrentOperations": 0}}