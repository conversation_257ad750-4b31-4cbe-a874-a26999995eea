{"selected": "hiveMind", "name": "Hive-Mind", "command": "npx claude-flow@latest hive-mind spawn --agents 5 --claude \"MASTER-WORKFLOW\"", "commands": ["npx claude-flow@latest hive-mind spawn \"MASTER-WORKFLOW\" --agents 5 --claude"], "score": 57, "stage": "active", "description": "Intelligent multi-agent coordination with specialized roles", "timeEstimate": "30 minutes - 4 hours", "agentCount": "4-6", "bestFor": ["Multi-feature development", "Fullstack applications", "Complex integrations", "Team-based development", "Medium-scale projects"], "reasoning": ["Medium complexity score (57/100) requires coordinated approach", "Active development requires efficient feature delivery"], "matchScore": 93, "alternatives": [{"name": "Hive-Mind + SPARC", "matchScore": 72, "command": "npx claude-flow hive-mind spawn --sparc"}, {"name": "Simple Swarm", "matchScore": 46, "command": "npx claude-flow swarm"}], "setupSteps": ["Initialize AI Dev OS environment", "Analyze project complexity", "Spawn Hive-Mind coordination system", "Configure specialized agent roles", "Set up cross-session memory", "Initialize parallel execution environment"]}