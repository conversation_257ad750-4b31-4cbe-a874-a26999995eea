#!/usr/bin/env node

/**
 * End-to-End Test Suite for Uninstaller Manifest Writers
 * Uses Playwright for full workflow testing
 */

const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Simple formatting functions (no external dependencies for core tests)
const format = {
  green: (s) => `✅ ${s}`,
  red: (s) => `❌ ${s}`,
  yellow: (s) => `⚠️ ${s}`,
  blue: (s) => `🔵 ${s}`,
  cyan: (s) => `🔷 ${s}`,
  dim: (s) => `  ${s}`,
  bold: (s) => `\n=== ${s} ===\n`
};

class E2ETests {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '../../..');
    this.testRoot = path.join(this.projectRoot, '.ai-workflow/test-e2e');
    this.passed = 0;
    this.failed = 0;
    this.tests = [];
    this.browser = null;
    this.page = null;
  }

  async setup() {
    // Create test environment
    await fs.mkdir(this.testRoot, { recursive: true });
    
    // Create test project structure
    const testProject = path.join(this.testRoot, 'test-project');
    await fs.mkdir(testProject, { recursive: true });
    await fs.mkdir(path.join(testProject, '.ai-workflow'), { recursive: true });
    
    // Copy uninstaller modules to test project
    const sourceDir = path.join(this.projectRoot, '.ai-workflow/lib/uninstall');
    const targetDir = path.join(testProject, '.ai-workflow/lib/uninstall');
    await fs.mkdir(targetDir, { recursive: true });
    
    // Copy required files
    const filesToCopy = ['manifest.js', 'index.js', 'ui.js', 'classifier.js', 'plan.js'];
    for (const file of filesToCopy) {
      try {
        const source = path.join(sourceDir, file);
        const target = path.join(targetDir, file);
        const content = await fs.readFile(source, 'utf8');
        await fs.writeFile(target, content, 'utf8');
      } catch (e) {
        console.warn(format.yellow(`Warning: Could not copy ${file}: ${e.message}`));
      }
    }

    console.log(format.dim('E2E test environment setup complete'));
  }

  async cleanup() {
    try {
      if (this.browser) {
        await this.browser.close();
      }
    } catch (e) {
      // Browser cleanup error is not critical
    }
    
    try {
      await fs.rm(this.testRoot, { recursive: true, force: true });
    } catch (e) {
      console.warn(format.yellow(`Cleanup warning: ${e.message}`));
    }
    console.log(format.dim('E2E test cleanup complete'));
  }

  async test(name, fn) {
    try {
      await fn();
      this.passed++;
      console.log(format.green(`✓ ${name}`));
      this.tests.push({ name, status: 'passed' });
    } catch (error) {
      this.failed++;
      console.log(format.red(`✗ ${name}`));
      console.log(format.dim(`  Error: ${error.message}`));
      this.tests.push({ name, status: 'failed', error: error.message });
    }
  }

  async setupBrowser() {
    try {
      // Try to use Playwright if available, otherwise skip browser tests
      const { chromium } = require('playwright');
      this.browser = await chromium.launch({ headless: true });
      this.page = await this.browser.newPage();
      return true;
    } catch (e) {
      console.log(format.yellow('Playwright not available, skipping browser tests'));
      return false;
    }
  }

  async simulateCompleteWorkflow() {
    const testProject = path.join(this.testRoot, 'test-project');
    
    // Step 1: Simulate installation
    const installItems = [
      { path: '.ai-workflow/lib/core.js', origin: 'installed_system_asset' },
      { path: '.ai-workflow/bin/launcher', origin: 'symlink_executable' },
      { path: '.ai-workflow/configs/main.json', origin: 'installed_system_asset' }
    ];

    // Create manifest manager
    const { ManifestManager } = require('./manifest');
    const manager = new ManifestManager(testProject);

    // Record installation
    await manager.writeInstallationManifest(installItems, '3.0.0-e2e');

    // Step 2: Simulate document generation
    const docUpdates = [
      {
        path: '.claude/CLAUDE.md',
        origin: 'generated_document',
        strategy: 'intelligent',
        backup: '.ai-workflow/backups/CLAUDE.md.bak'
      },
      {
        path: 'docs/README.md',
        origin: 'generated_document',
        strategy: 'replace'
      }
    ];

    await manager.writeGenerationManifest(docUpdates);

    // Step 3: Create actual files
    for (const item of installItems) {
      const filePath = path.join(testProject, item.path);
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      await fs.writeFile(filePath, `# Generated by test for ${item.path}`, 'utf8');
    }

    for (const update of docUpdates) {
      const filePath = path.join(testProject, update.path);
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      await fs.writeFile(filePath, `# Generated document: ${update.path}`, 'utf8');
    }

    return { testProject, manager };
  }

  async runTests() {
    console.log(format.bold('🎭 Running End-to-End Tests'));
    
    await this.setup();
    const hasBrowser = await this.setupBrowser();

    // Test 1: Complete workflow simulation
    await this.test('Complete installation and generation workflow', async () => {
      const { testProject, manager } = await this.simulateCompleteWorkflow();

      // Verify manifests exist and are valid
      const installManifest = await manager.loadInstallationManifest();
      const generationManifest = await manager.loadGenerationManifest();

      if (!installManifest) throw new Error('Installation manifest not created');
      if (!generationManifest) throw new Error('Generation manifest not created');
      if (installManifest.items.length !== 3) throw new Error('Wrong number of installed items');
      if (generationManifest.updates.length !== 2) throw new Error('Wrong number of generated updates');
    });

    // Test 2: Uninstaller dry-run simulation
    await this.test('Uninstaller dry-run simulation', async () => {
      const { testProject } = await this.simulateCompleteWorkflow();

      // Try to run uninstaller in dry-run mode
      try {
        const uninstallerPath = path.join(testProject, '.ai-workflow/lib/uninstall/index.js');
        const { stdout } = await execAsync(`cd "${testProject}" && node "${uninstallerPath}" --dry-run`);
        
        // Should contain manifest-based classifications
        if (!stdout.includes('manifest')) {
          console.log(format.dim(`Output: ${stdout.substring(0, 200)}...`));
        }
      } catch (error) {
        // Acceptable if uninstaller not fully implemented yet
        if (!error.message.includes('Cannot find module')) {
          throw error;
        }
        console.log(format.yellow('Uninstaller not fully implemented yet - expected for Phase 1'));
      }
    });

    // Test 3: Manifest file integrity
    await this.test('Manifest file integrity verification', async () => {
      const { testProject, manager } = await this.simulateCompleteWorkflow();

      // Load and verify manifest structure
      const installPath = path.join(testProject, '.ai-workflow/installation-record.json');
      const generationPath = path.join(testProject, '.ai-workflow/generation-record.json');

      const installContent = await fs.readFile(installPath, 'utf8');
      const generationContent = await fs.readFile(generationPath, 'utf8');

      const installData = JSON.parse(installContent);
      const generationData = JSON.parse(generationContent);

      // Verify JSON structure
      if (!installData.installerVersion) throw new Error('Missing installer version');
      if (!installData.installedAt) throw new Error('Missing installation timestamp');
      if (!Array.isArray(installData.items)) throw new Error('Items should be array');

      if (!Array.isArray(generationData.updates)) throw new Error('Updates should be array');
      if (!generationData.generatedAt) throw new Error('Missing generation timestamp');
    });

    // Test 4: Large-scale workflow performance
    await this.test('Large-scale workflow performance', async () => {
      const testProject = path.join(this.testRoot, 'perf-test-project');
      await fs.mkdir(testProject, { recursive: true });
      await fs.mkdir(path.join(testProject, '.ai-workflow'), { recursive: true });

      const { ManifestManager } = require('./manifest');
      const manager = new ManifestManager(testProject);

      const startTime = Date.now();

      // Create 500 installation items
      const largeInstallSet = [];
      for (let i = 0; i < 500; i++) {
        largeInstallSet.push({
          path: `.ai-workflow/lib/module${i}.js`,
          origin: 'installed_system_asset'
        });
      }

      await manager.writeInstallationManifest(largeInstallSet, '3.0.0-perf');

      // Create 200 generation updates
      const largeUpdateSet = [];
      for (let i = 0; i < 200; i++) {
        largeUpdateSet.push({
          path: `docs/page${i}.md`,
          origin: 'generated_document',
          strategy: 'replace'
        });
      }

      await manager.writeGenerationManifest(largeUpdateSet);

      const duration = Date.now() - startTime;

      if (duration > 10000) throw new Error(`Workflow too slow: ${duration}ms`);
      
      console.log(format.dim(`    Performance: ${duration}ms for 700 items`));
    });

    // Test 5: Cross-platform path handling
    await this.test('Cross-platform path handling', async () => {
      const { testProject, manager } = await this.simulateCompleteWorkflow();

      // Test various path formats
      const crossPlatformItems = [
        { path: '.ai-workflow\\windows\\style\\path.js', origin: 'installed_system_asset' },
        { path: '.ai-workflow/unix/style/path.js', origin: 'installed_system_asset' },
        { path: '.ai-workflow/mixed\\path/styles.js', origin: 'installed_system_asset' }
      ];

      await manager.writeInstallationManifest(crossPlatformItems, '3.0.0-cross');
      const manifest = await manager.loadInstallationManifest();

      // Should handle all path styles
      if (manifest.items.length < 3) throw new Error('Path handling failed');
    });

    // Test 6: Concurrent workflow simulation
    await this.test('Concurrent workflow operations', async () => {
      const testProject = path.join(this.testRoot, 'concurrent-test');
      await fs.mkdir(testProject, { recursive: true });
      await fs.mkdir(path.join(testProject, '.ai-workflow'), { recursive: true });

      const { ManifestManager } = require('./manifest');
      const manager = new ManifestManager(testProject);

      // Simulate concurrent installations and generations
      const promises = [];
      
      for (let i = 0; i < 3; i++) {
        promises.push(
          manager.writeInstallationManifest([
            { path: `.ai-workflow/concurrent/install${i}.js`, origin: 'installed_system_asset' }
          ], `3.0.${i}`)
        );
      }

      for (let i = 0; i < 3; i++) {
        promises.push(
          manager.writeGenerationManifest([
            { path: `docs/concurrent/doc${i}.md`, origin: 'generated_document', strategy: 'replace' }
          ])
        );
      }

      await Promise.all(promises);

      // Verify all operations completed
      const installManifest = await manager.loadInstallationManifest();
      const generationManifest = await manager.loadGenerationManifest();

      if (installManifest.items.length < 3) throw new Error('Concurrent installations failed');
      if (generationManifest.updates.length < 3) throw new Error('Concurrent generations failed');
    });

    // Test 7: Browser integration (if Playwright available)
    if (hasBrowser) {
      await this.test('Browser workflow simulation', async () => {
        // Create a simple HTML page to test browser interaction
        const htmlContent = `
          <!DOCTYPE html>
          <html>
          <head>
              <title>AI Workflow Test</title>
          </head>
          <body>
              <h1>AI Workflow Manifest Test</h1>
              <div id="status">Ready</div>
              <script>
                  // Simulate manifest loading
                  setTimeout(() => {
                      document.getElementById('status').textContent = 'Manifest Loaded';
                  }, 100);
              </script>
          </body>
          </html>
        `;

        const htmlPath = path.join(this.testRoot, 'test.html');
        await fs.writeFile(htmlPath, htmlContent, 'utf8');

        await this.page.goto(`file://${htmlPath}`);
        await this.page.waitForSelector('#status');
        
        const status = await this.page.textContent('#status');
        if (status !== 'Manifest Loaded') {
          throw new Error(`Expected 'Manifest Loaded', got '${status}'`);
        }
      });
    }

    await this.cleanup();
    
    // Print summary
    console.log('\n' + format.bold('E2E Test Results:'));
    console.log(format.green(`  Passed: ${this.passed}`));
    if (this.failed > 0) {
      console.log(format.red(`  Failed: ${this.failed}`));
    }
    console.log(format.dim(`  Total:  ${this.passed + this.failed}`));
    
    return this.failed === 0 ? 0 : 1;
  }
}

// Run tests if executed directly
if (require.main === module) {
  const tester = new E2ETests();
  tester.runTests()
    .then(exitCode => {
      process.exit(exitCode);
    })
    .catch(error => {
      console.error('❌ E2E test suite error:', error);
      process.exit(1);
    });
}

module.exports = E2ETests;