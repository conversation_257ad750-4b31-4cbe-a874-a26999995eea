{"name": "master-workflow-engine", "version": "0.1.0", "private": true, "type": "module", "description": "Core engine (CLI + Fastify API + SQLite) for Intelligent Workflow System", "main": "dist/api/server.js", "bin": {"mw-engine": "dist/cli/index.js"}, "scripts": {"build": "tsc -p tsconfig.json", "start": "node dist/api/server.js", "dev": "ts-node-esm src/api/server.ts", "health": "node -e \"fetch('http://127.0.0.1:'+ (process.env.MW_ENGINE_PORT||13800) +'/health').then(r=>r.text()).then(t=>console.log(t)).catch(e=>{console.error(e);process.exit(1)})\"", "cli": "node dist/cli/index.js", "migrate": "node dist/core/db.js migrate", "prepare": "npm install typescript@5.5.4 --no-save && npm install ts-node@10.9.2 --no-save"}, "engines": {"node": ">=20"}, "dependencies": {"better-sqlite3": "9.4.3", "commander": "12.1.0", "fastify": "4.28.1", "@fastify/cors": "9.0.1", "pino": "9.3.2"}, "devDependencies": {"pino-pretty": "11.2.2", "@types/better-sqlite3": "7.6.10", "@types/node": "22.5.5", "ts-node": "10.9.2", "typescript": "5.5.4"}}