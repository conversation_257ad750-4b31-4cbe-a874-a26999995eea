# Coding Standards - ${LANGUAGES:-General} Project

## Overview
These standards are customized based on your project's technology stack.

## Code Style

### JavaScript Standards
- Use 2 spaces for indentation
- Use single quotes for strings
- Add semicolons at the end of statements
- Use camelCase for variables and functions
- Use PascalCase for classes and components
- Use UPPER_SNAKE_CASE for constants
