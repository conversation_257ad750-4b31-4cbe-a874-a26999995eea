# Agent OS Instructions

## Project Context
- **Stage**: active
- **Complexity**: 54/100
- **Primary Language**: JavaScript
- **Primary Framework**: Not detected

## Development Standards

### JavaScript/TypeScript Standards
- Use ES6+ features (arrow functions, destructuring, async/await)
- Prefer functional programming patterns
- Use strict mode
- Implement proper error handling with try/catch
- Use TypeScript for type safety when available

### Python Standards
- Follow PEP 8 style guide
- Use type hints for function signatures
- Implement proper exception handling
- Use virtual environments for dependencies
- Write docstrings for all functions and classes

### Go Standards
- Follow effective Go guidelines
- Use gofmt for formatting
- Implement proper error handling
- Use goroutines for concurrency
- Write tests alongside code

## Stage-Specific Instructions (active)

- Maintain code quality
- Add new features systematically
- Refactor when necessary
- Ensure test coverage
- Document APIs and components
