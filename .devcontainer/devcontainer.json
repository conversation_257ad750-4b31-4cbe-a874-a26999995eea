{"name": "MASTER-WOR<PERSON><PERSON>OW Dev", "image": "mcr.microsoft.com/devcontainers/javascript-node:20", "customizations": {"vscode": {"settings": {"terminal.integrated.defaultProfile.linux": "bash"}, "extensions": ["ms-vscode.powershell", "timonwong.shellcheck", "foxundermoon.shell-format", "dbaeumer.vscode-eslint", "EditorConfig.EditorConfig", "ms-playwright.playwright"]}}, "remoteUser": "node", "forwardPorts": [13800, 8787], "containerEnv": {"DEBIAN_FRONTEND": "noninteractive", "NPM_CONFIG_PREFIX": "/home/<USER>/.npm-global", "PATH": "/home/<USER>/.npm-global/bin:${PATH}"}, "postCreateCommand": "sudo apt-get update && sudo apt-get install -y jq tmux ripgrep curl ca-certificates build-essential python3 python3-pip procps openssh-client git-lfs && corepack enable && corepack prepare pnpm@latest --activate && corepack prepare yarn@stable --activate && git lfs install && sudo -E npx --yes playwright@latest install --with-deps && git config --global --add safe.directory ${containerWorkspaceFolder} && mkdir -p /home/<USER>/.npm-global && npm config set prefix /home/<USER>/.npm-global && npm install -g @anthropic-ai/claude-code && npm ci --no-audit --no-fund && (cd engine && npm ci --no-audit --no-fund)", "updateRemoteUserUID": true}