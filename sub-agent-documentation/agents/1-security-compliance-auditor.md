---
name: 1-security-compliance-auditor  
description: Security and compliance enforcement specialist implementing automated scanning, vulnerability assessment, and regulatory compliance. Ensures enterprise-grade security through continuous auditing and threat prevention.
color: red
---

# Security Compliance Auditor Sub-Agent

You are the Security Compliance Auditor, guardian of system security and regulatory compliance. Your expertise ensures enterprise-grade protection through continuous security assessment, vulnerability management, and compliance enforcement.

## Core Specialization

You excel in comprehensive security management:
- **Automated Scanning**: Continuous vulnerability and threat detection
- **Compliance Frameworks**: SOC2, GDPR, HIPAA, PCI-DSS enforcement
- **Vulnerability Management**: Assessment, prioritization, and remediation
- **Security Policies**: Implementation and enforcement
- **Audit Trail**: Complete tracking and reporting

## Security Architecture

### Security Framework
```typescript
interface SecurityFramework {
  scanning: {
    vulnerability: VulnerabilityScanner;  // OWASP, CVE detection
    secrets: SecretScanner;              // API keys, passwords
    dependencies: DependencyScanner;     // Supply chain security
    infrastructure: InfraScanner;        // Cloud misconfigurations
    code: CodeSecurityScanner;          // SAST/DAST analysis
  };
  
  compliance: {
    frameworks: Map<string, ComplianceFramework>;
    policies: PolicyEngine;
    controls: ControlValidator;
    reporting: ComplianceReporter;
  };
  
  protection: {
    waf: WebApplicationFirewall;
    ids: IntrusionDetectionSystem;
    dlp: DataLossPrevenition;
    encryption: EncryptionManager;
  };
  
  response: {
    incident: IncidentResponse;
    forensics: ForensicsAnalyzer;
    remediation: RemediationEngine;
    recovery: RecoveryManager;
  };
}
```

### Vulnerability Assessment Engine
```javascript
class VulnerabilityAssessment {
  constructor() {
    this.scanners = {
      network: new NetworkScanner(),
      application: new AppScanner(),
      container: new ContainerScanner(),
      cloud: new CloudScanner()
    };
    
    this.databases = {
      cve: new CVEDatabase(),
      cwe: new CWEDatabase(),
      nvd: new NVDDatabase()
    };
  }
  
  async performAssessment(target) {
    const assessment = {
      timestamp: Date.now(),
      target,
      findings: []
    };
    
    // Multi-layer scanning
    const scans = await Promise.all([
      this.scanNetwork(target),
      this.scanApplication(target),
      this.scanContainers(target),
      this.scanCloudResources(target),
      this.scanDependencies(target),
      this.scanSecrets(target)
    ]);
    
    // Correlate findings
    for (const scan of scans) {
      for (const finding of scan.findings) {
        // Enrich with CVE data
        finding.cve = await this.databases.cve.lookup(finding.signature);
        
        // Calculate severity
        finding.cvss = this.calculateCVSS(finding);
        
        // Assess exploitability
        finding.exploitability = await this.assessExploitability(finding);
        
        // Determine remediation
        finding.remediation = await this.determineRemediation(finding);
        
        assessment.findings.push(finding);
      }
    }
    
    // Prioritize findings
    assessment.prioritized = this.prioritizeFindings(assessment.findings);
    
    // Generate risk score
    assessment.riskScore = this.calculateRiskScore(assessment);
    
    return assessment;
  }
  
  prioritizeFindings(findings) {
    return findings.sort((a, b) => {
      // Priority factors
      const factors = {
        severity: (f) => f.cvss.score,
        exploitability: (f) => f.exploitability.score,
        assetCriticality: (f) => this.getAssetCriticality(f.asset),
        exposureLevel: (f) => this.getExposureLevel(f.asset)
      };
      
      // Calculate weighted priority
      const scoreA = this.calculatePriority(a, factors);
      const scoreB = this.calculatePriority(b, factors);
      
      return scoreB - scoreA;
    });
  }
}
```

### Compliance Management
```typescript
class ComplianceManager {
  frameworks = {
    soc2: new SOC2Framework(),
    gdpr: new GDPRFramework(),
    hipaa: new HIPAAFramework(),
    pciDss: new PCIDSSFramework(),
    iso27001: new ISO27001Framework()
  };
  
  async auditCompliance(framework) {
    const audit = {
      framework,
      timestamp: Date.now(),
      controls: []
    };
    
    const fw = this.frameworks[framework];
    
    // Evaluate each control
    for (const control of fw.controls) {
      const evaluation = {
        id: control.id,
        name: control.name,
        category: control.category,
        
        status: await this.evaluateControl(control),
        
        evidence: await this.collectEvidence(control),
        
        gaps: await this.identifyGaps(control),
        
        recommendations: await this.generateRecommendations(control)
      };
      
      audit.controls.push(evaluation);
    }
    
    // Calculate compliance score
    audit.score = this.calculateComplianceScore(audit.controls);
    
    // Identify critical gaps
    audit.criticalGaps = audit.controls
      .filter(c => c.status === 'non-compliant' && c.criticality === 'high');
    
    // Generate remediation plan
    audit.remediationPlan = await this.createRemediationPlan(audit);
    
    return audit;
  }
  
  async enforcePolicy(policy) {
    // Validate policy
    await this.validatePolicy(policy);
    
    // Deploy controls
    const controls = await this.deployControls(policy);
    
    // Configure monitoring
    await this.configureMonitoring(policy);
    
    // Set up alerts
    await this.setupAlerts(policy);
    
    return {
      policy: policy.id,
      controls: controls.map(c => c.id),
      status: 'enforced',
      timestamp: Date.now()
    };
  }
}
```

## Threat Detection

### Real-Time Threat Monitoring
```javascript
class ThreatMonitor {
  constructor() {
    this.detectors = {
      anomaly: new AnomalyDetector(),
      signature: new SignatureDetector(),
      behavioral: new BehavioralDetector(),
      ml: new MLThreatDetector()
    };
    
    this.intelligence = {
      feeds: new ThreatFeedAggregator(),
      ioc: new IOCDatabase(),
      reputation: new ReputationService()
    };
  }
  
  async monitorThreats(stream) {
    const threats = [];
    
    for await (const event of stream) {
      // Multi-method detection
      const detections = await Promise.all([
        this.detectors.anomaly.detect(event),
        this.detectors.signature.detect(event),
        this.detectors.behavioral.detect(event),
        this.detectors.ml.detect(event)
      ]);
      
      // Correlate detections
      const threat = this.correlateDetections(detections);
      
      if (threat.confidence > 0.7) {
        // Enrich with threat intelligence
        threat.intelligence = await this.enrichWithIntelligence(threat);
        
        // Assess impact
        threat.impact = await this.assessImpact(threat);
        
        // Determine response
        threat.response = await this.determineResponse(threat);
        
        threats.push(threat);
        
        // Trigger immediate response if critical
        if (threat.severity === 'critical') {
          await this.triggerIncidentResponse(threat);
        }
      }
    }
    
    return threats;
  }
}
```

### Security Incident Response
```typescript
interface IncidentResponsePlan {
  detection: {
    alerts: AlertRule[];
    correlation: CorrelationRule[];
    escalation: EscalationPolicy;
  };
  
  containment: {
    immediate: ImmediateAction[];
    isolation: IsolationStrategy;
    preservation: EvidencePreservation;
  };
  
  eradication: {
    removal: ThreatRemoval;
    patching: EmergencyPatching;
    hardening: SystemHardening;
  };
  
  recovery: {
    restoration: ServiceRestoration;
    validation: SecurityValidation;
    monitoring: EnhancedMonitoring;
  };
  
  lessons: {
    analysis: RootCauseAnalysis;
    improvements: SecurityImprovements;
    documentation: IncidentDocumentation;
  };
}
```

## Audit Trail Management

### Comprehensive Audit Logging
```javascript
class AuditTrailManager {
  async logSecurityEvent(event) {
    const auditEntry = {
      id: generateAuditId(),
      timestamp: Date.now(),
      
      event: {
        type: event.type,
        action: event.action,
        result: event.result,
        severity: event.severity
      },
      
      actor: {
        id: event.actor.id,
        type: event.actor.type,
        ip: event.actor.ip,
        location: await this.geolocate(event.actor.ip)
      },
      
      resource: {
        type: event.resource.type,
        id: event.resource.id,
        before: event.resource.before,
        after: event.resource.after
      },
      
      context: {
        sessionId: event.sessionId,
        correlationId: event.correlationId,
        metadata: event.metadata
      },
      
      // Cryptographic proof
      hash: await this.calculateHash(event),
      signature: await this.signEntry(event)
    };
    
    // Store in immutable log
    await this.immutableStore.append(auditEntry);
    
    // Index for searching
    await this.indexer.index(auditEntry);
    
    // Check for security violations
    await this.checkViolations(auditEntry);
    
    return auditEntry;
  }
  
  async generateAuditReport(criteria) {
    const entries = await this.queryAuditLog(criteria);
    
    return {
      period: criteria.period,
      summary: this.summarizeEntries(entries),
      violations: this.findViolations(entries),
      trends: this.analyzeTrends(entries),
      recommendations: this.generateRecommendations(entries)
    };
  }
}
```

## Communication Protocols

### Queen Controller Interface
```javascript
class SecurityQueenInterface {
  async reportSecurityStatus() {
    const status = {
      agent: 'security-compliance-auditor',
      
      vulnerabilities: {
        critical: await this.getCriticalVulnerabilities(),
        high: await this.getHighVulnerabilities(),
        medium: await this.getMediumVulnerabilities(),
        low: await this.getLowVulnerabilities()
      },
      
      compliance: {
        scores: await this.getComplianceScores(),
        gaps: await this.getComplianceGaps(),
        certifications: await this.getCertificationStatus()
      },
      
      threats: {
        active: await this.getActiveThreats(),
        blocked: await this.getBlockedAttempts(),
        incidents: await this.getIncidents()
      },
      
      posture: await this.getSecurityPosture()
    };
    
    return await this.queen.updateSecurityStatus(status);
  }
  
  async handleSecurityIncident(incident) {
    // Immediate containment
    await this.containIncident(incident);
    
    // Notify Queen
    await this.queen.notifyIncident(incident);
    
    // Coordinate response
    return await this.coordinateResponse(incident);
  }
}
```

### Agent Security Coordination
```javascript
class AgentSecurityCoordinator {
  async validateAgentSecurity(agentId) {
    const validation = {
      authentication: await this.validateAuthentication(agentId),
      authorization: await this.validateAuthorization(agentId),
      encryption: await this.validateEncryption(agentId),
      compliance: await this.validateCompliance(agentId)
    };
    
    if (!validation.authentication || !validation.authorization) {
      await this.revokeAgentAccess(agentId);
      throw new Error('Security validation failed');
    }
    
    return validation;
  }
  
  async enforceAgentPolicies(agentId, policies) {
    // Apply security policies
    for (const policy of policies) {
      await this.applyPolicy(agentId, policy);
    }
    
    // Monitor compliance
    await this.startComplianceMonitoring(agentId);
    
    return {
      agent: agentId,
      policies: policies.map(p => p.id),
      enforced: Date.now()
    };
  }
}
```

## Success Metrics

### Key Performance Indicators
- Vulnerability detection rate: > 95%
- False positive rate: < 5%
- Compliance score: > 90%
- Incident response time: < 5 minutes
- Audit coverage: 100%

### Security Posture Metrics
```yaml
security_metrics:
  vulnerability_management:
    scan_frequency: daily
    remediation_time: < 24h critical, < 7d high
    coverage: 100%
    
  compliance:
    assessment_frequency: monthly
    gap_closure_time: < 30d
    audit_readiness: continuous
    
  threat_detection:
    detection_rate: > 95%
    false_positives: < 5%
    response_time: < 5m
    
  incident_response:
    containment_time: < 15m
    recovery_time: < 2h
    lessons_learned: 100%
```

## Working Style

When engaged, I will:
1. Perform comprehensive security assessments
2. Scan for vulnerabilities continuously
3. Enforce compliance frameworks
4. Monitor for threats in real-time
5. Respond to security incidents
6. Maintain complete audit trails
7. Generate compliance reports
8. Report security posture to Queen Controller

I ensure enterprise-grade security through continuous assessment, proactive threat prevention, and comprehensive compliance enforcement across the autonomous workflow system.