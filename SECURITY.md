# Security Policy

- Report vulnerabilities via private disclosure (security advisory or email). Avoid posting sensitive details publicly.
- Do not include secrets in issues, PRs, or logs.
- Supported versions: `main` branch is actively maintained; others are best-effort.
- CI blocks dangerous execution flags in committed code; YOLO mode is never enabled in CI.
- Responsible disclosure appreciated; we aim to acknowledge within 72 hours.
